<?php
/**
 * Template Name: الإشعارات
 */

// التحويل إذا لم يكن المستخدم مسجلاً
if (!is_user_logged_in()) {
    wp_redirect(wp_login_url(get_permalink()));
    exit;
}

// تضمين ملف نظام الإشعارات
require_once get_template_directory() . '/inc/load-notifications.php';

$user_id = get_current_user_id();
$paged = get_query_var('paged') ? get_query_var('paged') : 1;
$items_per_page = 20;
$offset = ($paged - 1) * $items_per_page;

// الحصول على الإشعارات
$notifications = sekai_get_user_notifications($user_id, $items_per_page, $offset);

// الحصول على إجمالي عدد الإشعارات
global $wpdb;
$table_name = $wpdb->prefix . 'sekai_notifications';
$total_notifications = $wpdb->get_var($wpdb->prepare(
    "SELECT COUNT(*) FROM $table_name WHERE user_id = %d",
    $user_id
));

$total_pages = ceil($total_notifications / $items_per_page);

// تحديث حالة الإشعارات إلى مقروءة
sekai_mark_all_notifications_as_read($user_id);

get_header();
?>

<div class="container py-5">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">الإشعارات</h1>
        
        <?php if (!empty($notifications)) : ?>
        <button id="clearAllNotifications" class="btn btn-sm btn-outline-danger">
            <i class="fas fa-trash-alt"></i> حذف الكل
        </button>
        <?php endif; ?>
    </div>

    <?php if (empty($notifications)) : ?>
    <div class="card shadow-sm">
        <div class="card-body text-center py-5">
            <i class="fas fa-bell-slash fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد إشعارات.</h5>
            <p class="text-muted mb-0">ستظهر هنا الإشعارات الخاصة بالروايات المفضلة والمحتوى الخاص بك.</p>
        </div>
    </div>
    <?php else : ?>
    <div class="card shadow-sm">
        <div class="list-group list-group-flush" id="notificationsList">
            <?php foreach ($notifications as $notification) : ?>
                <?php 
                $icon_class = 'fa-bell';
                $badge_class = 'bg-primary';
                
                // تحديد الأيقونة ولون الشارة بناءً على نوع الإشعار
                if ($notification->type === 'new_chapter') {
                    $icon_class = 'fa-book-open';
                    $badge_class = 'bg-success';
                } elseif ($notification->type === 'approved_content') {
                    $icon_class = 'fa-check-circle';
                    $badge_class = 'bg-info';
                }
                
                // تنسيق التاريخ
                $date = new DateTime($notification->created_at);
                $now = new DateTime();
                $interval = $date->diff($now);
                
                if ($interval->days == 0) {
                    if ($interval->h == 0) {
                        $time_ago = sprintf(_n('%d دقيقة', '%d دقائق', $interval->i), $interval->i);
                    } else {
                        $time_ago = sprintf(_n('%d ساعة', '%d ساعات', $interval->h), $interval->h);
                    }
                } elseif ($interval->days < 7) {
                    $time_ago = sprintf(_n('%d يوم', '%d أيام', $interval->days), $interval->days);
                } else {
                    $time_ago = $date->format('Y-m-d');
                }
                
                // تحديد رابط الإشعار
                $notification_link = '#';
                if ($notification->reference_id && $notification->reference_type) {
                    if (in_array($notification->reference_type, array('novel', 'chapter'))) {
                        $notification_link = get_permalink($notification->reference_id);
                    }
                }
                ?>
                <a href="<?php echo esc_url($notification_link); ?>" class="list-group-item list-group-item-action notification-item <?php echo $notification->is_read ? '' : 'unread'; ?>" data-id="<?php echo $notification->id; ?>">
                    <div class="d-flex align-items-center">
                        <div class="notification-icon">
                            <span class="badge rounded-circle <?php echo $badge_class; ?>">
                                <i class="fas <?php echo $icon_class; ?>"></i>
                            </span>
                        </div>
                        <div class="ms-3 flex-grow-1">
                            <div class="notification-content"><?php echo esc_html($notification->content); ?></div>
                            <div class="notification-time text-muted small">
                                <i class="far fa-clock"></i> <?php echo esc_html($time_ago); ?>
                            </div>
                        </div>
                        <div class="notification-actions">
                            <button class="btn btn-sm btn-link text-danger delete-notification" data-id="<?php echo $notification->id; ?>">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                </a>
            <?php endforeach; ?>
        </div>
    </div>
    
    <?php if ($total_pages > 1) : ?>
    <div class="pagination-wrapper mt-4">
        <nav aria-label="تنقل بين الصفحات">
            <ul class="pagination justify-content-center">
                <?php
                $current_url = get_pagenum_link(1);
                $current_url = remove_query_arg('paged', $current_url);
                
                // زر الصفحة السابقة
                if ($paged > 1) :
                    $prev_url = add_query_arg('paged', $paged - 1, $current_url);
                ?>
                <li class="page-item">
                    <a class="page-link" href="<?php echo esc_url($prev_url); ?>" aria-label="السابق">
                        <span aria-hidden="true">&laquo;</span>
                    </a>
                </li>
                <?php endif; ?>
                
                <?php
                // أرقام الصفحات
                $start_page = max(1, $paged - 2);
                $end_page = min($total_pages, $paged + 2);
                
                for ($i = $start_page; $i <= $end_page; $i++) :
                    $page_url = add_query_arg('paged', $i, $current_url);
                    $is_current = $i == $paged;
                ?>
                <li class="page-item <?php echo $is_current ? 'active' : ''; ?>">
                    <a class="page-link" href="<?php echo esc_url($page_url); ?>"><?php echo $i; ?></a>
                </li>
                <?php endfor; ?>
                
                <?php
                // زر الصفحة التالية
                if ($paged < $total_pages) :
                    $next_url = add_query_arg('paged', $paged + 1, $current_url);
                ?>
                <li class="page-item">
                    <a class="page-link" href="<?php echo esc_url($next_url); ?>" aria-label="التالي">
                        <span aria-hidden="true">&raquo;</span>
                    </a>
                </li>
                <?php endif; ?>
            </ul>
        </nav>
    </div>
    <?php endif; ?>
    <?php endif; ?>
</div>

<style>
.notification-icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.notification-icon .badge {
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
}

.notification-item {
    transition: all 0.2s ease;
    border-right: 3px solid transparent;
}

.notification-item.unread {
    background-color: rgba(var(--bs-primary-rgb), 0.05);
    border-right-color: var(--bs-primary);
}

.notification-item:hover {
    background-color: var(--bs-light);
}

.notification-actions {
    opacity: 0;
    transition: opacity 0.2s ease;
}

.notification-item:hover .notification-actions {
    opacity: 1;
}
</style>

<script>
jQuery(document).ready(function($) {
    // حذف إشعار واحد
    $('.delete-notification').on('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        
        const notificationId = $(this).data('id');
        const $notificationItem = $(this).closest('.notification-item');
        
        $.ajax({
            url: '<?php echo admin_url('admin-ajax.php'); ?>',
            type: 'POST',
            data: {
                action: 'delete_notification',
                notification_id: notificationId,
                nonce: '<?php echo wp_create_nonce('sekai_ajax_nonce'); ?>'
            },
            success: function(response) {
                if (response.success) {
                    $notificationItem.slideUp(300, function() {
                        $(this).remove();
                        
                        // إذا لم يكن هناك إشعارات، قم بتحديث الصفحة
                        if ($('.notification-item').length === 0) {
                            location.reload();
                        }
                    });
                }
            }
        });
    });
    
    // حذف جميع الإشعارات
    $('#clearAllNotifications').on('click', function() {
        if (confirm('هل أنت متأكد من حذف جميع الإشعارات؟')) {
            $.ajax({
                url: '<?php echo admin_url('admin-ajax.php'); ?>',
                type: 'POST',
                data: {
                    action: 'delete_all_notifications',
                    nonce: '<?php echo wp_create_nonce('sekai_ajax_nonce'); ?>'
                },
                success: function(response) {
                    if (response.success) {
                        location.reload();
                    }
                }
            });
        }
    });
});
</script>

<?php get_footer(); ?>
