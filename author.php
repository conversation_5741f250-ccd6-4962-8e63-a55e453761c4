<?php
/**
 * The template for displaying author pages
 */

get_header();

// Get author information
$author = get_user_by('slug', get_query_var('author_name'));
$author_id = $author->ID;
$avatar_url = get_avatar_url($author_id, ['size' => 400]);

// Get user meta
$bio = get_the_author_meta('description', $author_id);
$website = get_the_author_meta('user_url', $author_id);
$twitter = get_the_author_meta('twitter', $author_id);
$discord = get_the_author_meta('discord', $author_id);

// Get user role
$user_roles = $author->roles;
$role = in_array('administrator', $user_roles) ? 'admin' :
        (in_array('translator', $user_roles) ? 'translator' : 'reader');

// دالة للتحقق إذا كان العضو فقط مشترك (subscriber)
function sekaiplus_is_subscriber_only($user) {
    return in_array('subscriber', (array) $user->roles) && count($user->roles) === 1;
}
$is_subscriber_only = apply_filters('author_page_is_subscriber_only', sekaiplus_is_subscriber_only($author), $author);

// Get counts
$novels_count = count_user_posts($author_id, 'novel');
$chapters_count = count_user_posts($author_id, 'chapter');
$comments_count = get_comments(['user_id' => $author_id, 'count' => true]);

// جلب الروايات المفضلة
$bookmarked_novels = get_user_meta($author_id, '_novel_bookmarks', true);
$fav_novels = [];
if (!empty($bookmarked_novels) && is_array($bookmarked_novels)) {
    $fav_novels = get_posts([
        'post_type' => 'novel',
        'post__in' => $bookmarked_novels,
        'orderby' => 'post__in',
        'posts_per_page' => 12
    ]);
}

// Pagination
$paged = (get_query_var('paged')) ? get_query_var('paged') : 1;
$posts_per_page = 8;

// Enqueue necessary styles
wp_enqueue_style('profile-modern', get_template_directory_uri() . '/css/profile-modern.css');
wp_enqueue_style('modern-badges', get_template_directory_uri() . '/css/modern-badges.css');
wp_enqueue_style('font-awesome', 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css');

// Include Elite badge functionality if it exists
if (file_exists(get_template_directory() . '/inc/elite-badge.php')) {
    require_once get_template_directory() . '/inc/elite-badge.php';
}

// Get user's cover image or default
$cover_image = get_user_meta($author_id, 'cover_image', true);
$cover_style = $cover_image ? "background-image: url('" . esc_url($cover_image) . "')" : "background-color: #007bff";
?>

<style>
:root {
    --profile-primary: #3498db;
    --profile-secondary: #2ecc71;
    --profile-accent: #f39c12;
    --profile-dark: #343a40;
    --profile-light: #ecf0f1;
    --profile-bg: #f8f9fa;
    --profile-card: #ffffff;
    --profile-text: #2c3e50;
    --profile-text-light: #7f8c8d;
    --profile-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    --profile-admin: #e74c3c;
    --profile-translator: #27ae60;
    --profile-reader: #7f8c8d;
    --profile-border-radius: 15px;
}

/* Dark Mode Support */
.dark-mode {
    --profile-bg: #1a1a2e;
    --profile-card: #343a40;
    --profile-text: #e3e3e3;
    --profile-text-light: #a8a8a8;
    --profile-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
}

.profile-modern {
    background-color: var(--profile-bg);
    padding: 30px 0;
    font-family: 'Cairo', sans-serif;
    color: var(--profile-text);
}

.profile-header-modern {
    position: relative;
    border-radius: var(--profile-border-radius);
    overflow: hidden;
    margin-bottom: 30px;
    box-shadow: var(--profile-shadow);
}

.profile-cover-modern {
    height: 300px;
    width: 100%;
    background-size: cover;
    background-position: center;
}

.profile-header-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to bottom, rgba(0,0,0,0.2) 0%, rgba(0,0,0,0.6) 100%);
    z-index: 1;
}

.profile-info-modern {
    position: relative;
    z-index: 2;
    display: flex;
    padding: 30px;
    color: #fff;
    align-items: center;
}

.avatar-container {
    position: relative;
    margin-right: 30px;
}

.profile-avatar {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    border: 5px solid var(--profile-card);
    box-shadow: var(--profile-shadow);
    object-fit: cover;
}

.admin-badge-overlay {
    position: absolute;
    bottom: 0;
    right: 10px;
    width: 35px;
    height: 35px;
    background-color: var(--profile-admin);
    color: #fff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--profile-shadow);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(231, 76, 60, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(231, 76, 60, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(231, 76, 60, 0);
    }
}

.avatar-edit-btn {
    position: absolute;
    top: 5px;
    right: 5px;
    background-color: var(--profile-dark);
    color: #fff;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0.8;
    transition: all 0.3s ease;
    text-decoration: none;
}

.avatar-edit-btn:hover {
    opacity: 1;
    transform: scale(1.1);
    color: #fff;
}

.profile-details-modern {
    flex: 1;
}

.profile-name-section {
    margin-bottom: 15px;
}

.profile-name {
    font-size: 32px;
    font-weight: 700;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.edit-name-btn {
    margin-left: 10px;
    color: #fff;
    opacity: 0.7;
    transition: all 0.3s ease;
    font-size: 16px;
}

.edit-name-btn:hover {
    opacity: 1;
    color: #fff;
}

/* Badges Section - Modern & Professional */
.profile-badges-section {
    background-color: var(--profile-card);
    border-radius: var(--profile-border-radius);
    padding: 20px;
    margin-bottom: 30px;
    box-shadow: var(--profile-shadow);
    position: relative;
    overflow: hidden;
    border-top: 4px solid var(--profile-primary);
}

.badges-title {
    font-size: 18px;
    font-weight: 700;
    margin-bottom: 20px;
    color: var(--profile-text);
    display: flex;
    align-items: center;
    padding-bottom: 10px;
    border-bottom: 1px solid rgba(0,0,0,0.05);
}

.badges-title i {
    margin-left: 10px;
    color: var(--profile-primary);
}

.profile-badges {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
}

.badge {
    display: inline-flex;
    align-items: center;
    padding: 8px 18px;
    border-radius: 50px;
    font-size: 13px;
    font-weight: 600;
    top: 0%;
    box-shadow: var(--profile-shadow);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    cursor: pointer;
    transform: translateY(0);
}

.badge::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.1);
    transform: translateX(-100%);
    transition: transform 0.5s ease;
}

.badge:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
}

.badge:hover::before {
    transform: translateX(0);
}

.badge i {
    margin-left: 10px;
    font-size: 16px;
    transition: transform 0.3s ease;
}

.badge:hover i {
    transform: scale(1.2);
}

.admin-badge {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: #fff;
    display: inline-flex;
    align-items: center;
    padding: 8px 18px;
    border-radius: 50px;
    font-size: 13px;
    font-weight: 600;
    top: 0%;
    box-shadow: var(--profile-shadow);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    cursor: pointer;
    transform: translateY(0);
}

.translator-badge {
    background: linear-gradient(135deg, #239d59, #517736);
    color: #fff;
    display: inline-flex
;
    align-items: center;
    padding: 8px 18px;
    border-radius: 50px;
    font-size: 13px;
    font-weight: 600;
    top: 0%;
    box-shadow: var(--profile-shadow);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    cursor: pointer;
    transform: translateY(0);
}

.reader-badge {
    background: linear-gradient(135deg, #7f8c8d, #576364);
    color: #fff;
    display: inline-flex;
    align-items: center;
    padding: 8px 18px;
    border-radius: 50px;
    font-size: 13px;
    font-weight: 600;
    top: 0%;
    box-shadow: var(--profile-shadow);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    cursor: pointer;
    transform: translateY(0);
}

.elite-badge {
    background: linear-gradient(135deg, #f39c12, #e67e22);
    color: #fff;
    display: inline-flex;
    align-items: center;
    padding: 8px 18px;
    border-radius: 50px;
    font-size: 13px;
    font-weight: 600;
    top: 0%;
    box-shadow: var(--profile-shadow);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    cursor: pointer;
    transform: translateY(0);
}

/* Badge Animations */
@keyframes badgePulse {
    0% {
        box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.4);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(255, 255, 255, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
    }
}

@keyframes badgeGlow {
    0%, 100% {
        filter: brightness(1);
    }
    50% {
        filter: brightness(1.3);
    }
}

@keyframes badgeShine {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

.badge.premium-badge {
    animation: badgePulse 2s infinite, badgeShine 3s ease infinite;
    background: linear-gradient(45deg, #ffd700, #ffcc00);
    background-size: 200% 200%;
}

.admin-badge {
    animation: badgeGlow 3s ease-in-out infinite;
}

.translator-badge {
    animation: badgeShine 4s ease infinite;
    background: linear-gradient(135deg, #71aa1e, #e8fe0699, #022913);
    background-size: 200% 200%;
}

.profile-meta {
    color: rgba(255, 255, 255, 0.9);
    font-size: 14px;
}

.profile-meta i {
    margin-left: 5px;
}

.join-date {
    display: inline-flex;
    align-items: center;
}

.profile-social {
    margin-top: 15px;
}

.social-icons {
    display: flex;
    gap: 10px;
    margin-bottom: 10px;
}

.social-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    font-size: 18px;
    transition: all 0.3s ease;
    text-decoration: none;
}

.social-icon:hover {
    transform: translateY(-5px);
    color: #fff;
}

.website-icon {
    background: linear-gradient(135deg, #2980b9, #3498db);
}

.twitter-icon {
    background: linear-gradient(135deg, #1da1f2, #0d8eda);
}

.instagram-icon {
    background: linear-gradient(135deg, #833ab4, #fd1d1d, #fcb045);
}

.discord-icon {
    background: linear-gradient(135deg, #7289da, #5865f2);
}

.telegram-icon {
    background: linear-gradient(135deg, #2aabee, #229ED9);
}

.edit-social-btn {
    display: inline-flex;
    align-items: center;
    padding: 8px 15px;
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 30px;
    color: #fff;
    font-size: 14px;
    transition: all 0.3s ease;
    text-decoration: none;
}

.edit-social-btn:hover {
    background-color: rgba(255, 255, 255, 0.3);
    color: #fff;
}

.edit-social-btn i {
    margin-left: 8px;
}

/* Team Section - Modern & Professional */
.team-section-modern {
    margin-bottom: 30px;
}

.team-card-modern {
    background-color: var(--profile-card);
    border-radius: var(--profile-border-radius);
    padding: 25px;
    box-shadow: var(--profile-shadow);
    border: 1px solid rgba(0,0,0,0.05);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.team-card-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, var(--profile-secondary), #27ae60);
}

.team-card-modern:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 30px rgba(0,0,0,0.15);
}

.team-header-modern {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 20px;
}

.team-icon-container {
    position: relative;
    flex-shrink: 0;
}

.team-logo {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid var(--profile-secondary);
    box-shadow: 0 4px 15px rgba(46, 204, 113, 0.3);
}

.team-icon-default {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--profile-secondary), #27ae60);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    font-size: 28px;
    box-shadow: 0 4px 15px rgba(46, 204, 113, 0.3);
}

.leader-badge-overlay {
    position: absolute;
    top: -5px;
    right: -5px;
    width: 28px;
    height: 28px;
    background: linear-gradient(135deg, #f39c12, #e67e22);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    font-size: 12px;
    box-shadow: 0 3px 10px rgba(243, 156, 18, 0.4);
    animation: pulse 2s infinite;
}

.team-info-modern {
    flex: 1;
}

.team-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--profile-text-light);
    margin: 0 0 8px 0;
    display: flex;
    align-items: center;
}

.team-title i {
    margin-left: 8px;
    color: var(--profile-secondary);
}

.team-name {
    margin-bottom: 12px;
}

.team-link {
    font-size: 20px;
    font-weight: 700;
    color: var(--profile-text);
    text-decoration: none;
    transition: all 0.3s ease;
    position: relative;
}

.team-link:hover {
    color: var(--profile-secondary);
}

.team-link::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(135deg, var(--profile-secondary), #27ae60);
    transition: width 0.3s ease;
}

.team-link:hover::after {
    width: 100%;
}

.team-role-badge {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 6px 14px;
    border-radius: 25px;
    font-size: 13px;
    font-weight: 600;
    margin-bottom: 8px;
    transition: all 0.3s ease;
}

.team-role-badge.leader-role {
    background: linear-gradient(135deg, #f39c12, #e67e22);
    color: #fff;
    box-shadow: 0 3px 10px rgba(243, 156, 18, 0.3);
}

.team-role-badge.member-role {
    background: linear-gradient(135deg, var(--profile-secondary), #27ae60);
    color: #fff;
    box-shadow: 0 3px 10px rgba(46, 204, 113, 0.3);
}

.team-joined-date {
    color: var(--profile-text-light);
    font-size: 13px;
    display: flex;
    align-items: center;
}

.team-joined-date i {
    margin-left: 6px;
    color: var(--profile-secondary);
}

.team-actions-modern {
    text-align: center;
    padding-top: 15px;
    border-top: 1px solid rgba(0,0,0,0.05);
}

.btn-visit-team {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 10px 20px;
    background: linear-gradient(135deg, var(--profile-secondary), #27ae60);
    color: #fff;
    text-decoration: none;
    border-radius: 25px;
    font-size: 14px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(46, 204, 113, 0.3);
}

.btn-visit-team:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(46, 204, 113, 0.4);
    color: #fff;
}

/* Responsive adjustments for team section */
@media (max-width: 768px) {
    .team-header-modern {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }

    .team-info-modern {
        text-align: center;
    }
}

/* Statistics Cards */
.profile-stats-modern {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background-color: var(--profile-card);
    border-radius: var(--profile-border-radius);
    padding: 20px;
    box-shadow: var(--profile-shadow);
    text-align: center;
    transition: all 0.3s ease;
    border-top: 4px solid var(--profile-primary);
}

.stat-card:nth-child(2) {
    border-top-color: var(--profile-secondary);
}

.stat-card:nth-child(3) {
    border-top-color: var(--profile-accent);
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-icon {
    font-size: 36px;
    color: var(--profile-primary);
    margin-bottom: 15px;
}

.stat-card:nth-child(2) .stat-icon {
    color: var(--profile-secondary);
}

.stat-card:nth-child(3) .stat-icon {
    color: var(--profile-accent);
}

.stat-value {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 8px;
    color: var(--profile-text);
}

.stat-label {
    color: var(--profile-text-light);
    font-size: 14px;
}

/* Content Tabs Styling */
.profile-tabs-modern {
    background-color: var(--profile-card);
    border-radius: var(--profile-border-radius);
    box-shadow: var(--profile-shadow);
    overflow: hidden;
}

.tabs-nav-modern {
    display: flex;
    flex-wrap: nowrap;
    overflow-x: auto;
    background-color: var(--profile-card);
    border-bottom: 1px solid rgba(0,0,0,0.1);
    padding: 0;
    position: sticky;
    top: 60px;
    z-index: 10;
}

.tab-link-modern {
    padding: 15px 25px;
    color: var(--profile-text-light);
    font-weight: 600;
    text-decoration: none;
    position: relative;
    transition: all 0.3s ease;
    white-space: nowrap;
    display: flex;
    align-items: center;
    border-bottom: 3px solid transparent;
}

.tab-link-modern i {
    margin-left: 8px;
    font-size: 16px;
}

.tab-link-modern:hover {
    color: var(--profile-primary);
}

.tab-link-modern.active {
    color: var(--profile-primary);
    border-bottom: 3px solid var(--profile-primary);
}

.tab-content-modern {
    padding: 30px;
}

.tab-pane-modern {
    display: none;
}

.tab-pane-modern.active {
    display: block;
    animation: fadeIn 0.5s ease forwards;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Content Cards */
.content-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 20px;
}

/* Novel Cards - Improved Design */
.novel-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
    gap: 15px;
}

.content-card {
    background-color: var(--profile-card);
    border-radius: var(--profile-border-radius);
    overflow: hidden;
    box-shadow: var(--profile-shadow);
    transition: all 0.3s ease;
    border: 1px solid rgba(0,0,0,0.05);
    height: 100%;
    display: flex;
    flex-direction: column;
}

.content-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0,0,0,0.15);
}

/* Standard content card image */
.content-card-image {
    height: 180px;
    background-size: cover;
    background-position: center;
    position: relative;
}

/* Novel card image - improved ratio and display */
.novel-card {
    position: relative;
    transition: all 0.3s ease;
    border-radius: var(--profile-border-radius);
    overflow: hidden;
    box-shadow: var(--profile-shadow);
    background-color: var(--profile-card);
    display: block; /* ضمان العرض على الأجهزة المحمولة */
    width: 100%; /* ضمان العرض الكامل */
}

.novel-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.2);
}

.novel-card-cover {
    position: relative;
    padding-top: 150%; /* 2:3 aspect ratio for novel covers */
    overflow: hidden;
    display: block; /* ضمان العرض على الأجهزة المحمولة */
}

.novel-card-cover img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
    display: block; /* ضمان العرض على الأجهزة المحمولة */
}

.novel-card:hover .novel-card-cover img {
    transform: scale(1.05);
}

.novel-card-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(to top, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0.4) 60%, rgba(0,0,0,0) 100%);
    padding: 30px 10px 10px;
    z-index: 2; /* ضمان ظهور النص فوق الصورة */
}

/* Mobile Responsiveness - تحسين التوافق مع الأجهزة المحمولة */
@media (max-width: 767px) {
    .novel-grid {
        grid-template-columns: repeat(auto-fill, minmax(130px, 1fr));
        gap: 10px;
    }

    .novel-card-overlay {
        padding: 20px 8px 8px;
    }

    .novel-card-overlay h3 {
        font-size: 12px !important;
        line-height: 1.3 !important;
    }

    .novel-card-overlay .content-card-meta {
        font-size: 10px !important;
    }
}

/* Extra Small Devices */
@media (max-width: 480px) {
    .novel-grid {
        grid-template-columns: repeat(auto-fill, minmax(110px, 1fr));
        gap: 8px;
    }
}

.content-card-image::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: linear-gradient(to bottom, rgba(0,0,0,0) 50%, rgba(0,0,0,0.7) 100%);
}

.content-card-body {
    padding: 20px;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.content-card-title {
    margin-top: 0;
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 15px;
}

.content-card-title a {
    color: var(--profile-text);
    text-decoration: none;
    transition: color 0.3s ease;
}

.content-card-title a:hover {
    color: var(--profile-primary);
}

.content-card-meta {
    margin-top: auto;
    display: flex;
    justify-content: space-between;
    color: var(--profile-text-light);
    font-size: 14px;
}

.content-card-meta i {
    margin-left: 5px;
}

/* Comments Section */
.comments-list {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.comment-card {
    background-color: var(--profile-card);
    border-radius: var(--profile-border-radius);
    padding: 20px;
    box-shadow: var(--profile-shadow);
    transition: all 0.3s ease;
    border-left: 4px solid var(--profile-primary);
}

.comment-card:hover {
    transform: translateX(5px);
}

.comment-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
    border-bottom: 1px solid rgba(0,0,0,0.05);
    padding-bottom: 10px;
}

.comment-novel a {
    color: var(--profile-primary);
    font-weight: 600;
    text-decoration: none;
}

.comment-date {
    color: var(--profile-text-light);
    font-size: 14px;
}

.comment-content {
    color: var(--profile-text);
    font-size: 16px;
    line-height: 1.5;
}

/* About Section */
.about-section {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 25px;
}

@media (max-width: 768px) {
    .about-section {
        grid-template-columns: 1fr;
    }
}

.bio-card, .contact-card {
    background-color: var(--profile-card);
    border-radius: var(--profile-border-radius);
    padding: 25px;
    box-shadow: var(--profile-shadow);
}

.bio-card h3, .contact-card h3 {
    margin-top: 0;
    border-bottom: 2px solid rgba(0,0,0,0.05);
    padding-bottom: 15px;
    margin-bottom: 20px;
    color: var(--profile-text);
    display: flex;
    align-items: center;
}

.bio-card h3 i, .contact-card h3 i {
    margin-left: 10px;
    color: var(--profile-primary);
}

.bio-content {
    color: var(--profile-text);
    line-height: 1.8;
}

.contact-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.contact-list li {
    padding: 10px 0;
    display: flex;
    align-items: center;
    border-bottom: 1px solid rgba(0,0,0,0.05);
}

.contact-list li:last-child {
    border-bottom: none;
}

.contact-list li i {
    width: 30px;
    height: 30px;
    background-color: var(--profile-primary);
    color: #fff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 15px;
    font-size: 14px;
}

.contact-list li a {
    color: var(--profile-text);
    text-decoration: none;
    transition: color 0.3s ease;
}

.contact-list li a:hover {
    color: var(--profile-primary);
}

/* No Content Message */
.no-content-message {
    text-align: center;
    padding: 40px;
    background-color: var(--profile-card);
    border-radius: var(--profile-border-radius);
    box-shadow: var(--profile-shadow);
    color: var(--profile-text-light);
}

.no-content-message i {
    font-size: 48px;
    margin-bottom: 15px;
    color: var(--profile-text-light);
    opacity: 0.5;
}

.no-content-message p {
    font-size: 18px;
    margin: 0;
}

.no-content {
    color: var(--profile-text-light);
    font-style: italic;
}

/* Animation */
.animate-fade-in {
    opacity: 0;
    animation: fadeIn 0.6s ease forwards;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .profile-info-modern {
        flex-direction: column;
        align-items: center;
        text-align: center;
    }

    .avatar-container {
        margin-right: 0;
        margin-bottom: 20px;
    }

    .profile-name {
        justify-content: center;
    }

    .profile-badges {
        justify-content: center;
    }

    .social-icons {
        justify-content: center;
    }

    .edit-social-btn {
        margin: 0 auto;
        display: table;
    }
}

/* Modal styles for profile editing */
.modal-backdrop {
    display: none;
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background-color: rgba(0,0,0,0.5);
    z-index: 1000;
    align-items: center;
    justify-content: center;
}

.modal-container {
    width: 90%;
    max-width: 500px;
    background-color: var(--profile-card);
    border-radius: var(--profile-border-radius);
    box-shadow: var(--profile-shadow);
    padding: 30px;
    position: relative;
    max-height: 80vh;
    overflow-y: auto;
}

.modal-close {
    position: absolute;
    top: 15px;
    right: 15px;
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: var(--profile-text);
    transition: color 0.3s ease;
}

.modal-close:hover {
    color: var(--profile-primary);
}

.modal-title {
    margin-top: 0;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid rgba(0,0,0,0.1);
    color: var(--profile-text);
    font-size: 24px;
    font-weight: 600;
}

.modal-body {
    margin-bottom: 20px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: var(--profile-text);
}

.form-control {
    width: 100%;
    padding: 12px;
    border: 1px solid rgba(0,0,0,0.1);
    border-radius: 5px;
    font-size: 16px;
    transition: border-color 0.3s ease;
}

.form-control:focus {
    outline: none;
    border-color: var(--profile-primary);
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
}

.btn {
    padding: 12px 25px;
    border: none;
    border-radius: 5px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-primary {
    background-color: var(--profile-primary);
    color: #fff;
}

.btn-primary:hover {
    background-color: #2980b9;
}

.btn-secondary {
    background-color: #95a5a6;
    color: #fff;
}

.btn-secondary:hover {
    background-color: #7f8c8d;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

/* Form controls within modals */
.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
}

.form-control {
    width: 100%;
    padding: 10px;
    border: 1px solid #ccc;
    border-radius: 5px;
    font-family: inherit;
}

.btn {
    padding: 8px 16px;
    border-radius: 5px;
    cursor: pointer;
    font-weight: 500;
    border: none;
}

.btn-primary {
    background-color: var(--profile-primary);
    color: white;
}

.btn-secondary {
    background-color: #95a5a6;
    color: white;
}

/* Gravatar Preview Styling */
.gravatar-instructions {
    line-height: 1.6;
}

.gravatar-preview {
    display: flex;
    gap: 20px;
    margin: 20px 0;
}

.preview-section {
    flex: 1;
    text-align: center;
}

.preview-section h4 {
    margin-bottom: 12px;
    color: var(--profile-text);
}

.preview-avatar {
    border-radius: 50%;
    box-shadow: 0 3px 8px rgba(0,0,0,0.2);
}

.header-preview {
    box-shadow: 0 3px 8px rgba(0,0,0,0.2);
}

.gravatar-info {
    background-color: rgba(52, 152, 219, 0.1);
    border-radius: 8px;
    padding: 12px;
    margin-top: 15px;
}

.gravatar-info i {
    color: var(--profile-primary);
    margin-left: 5px;
}

/* For RTL language support */
[dir="rtl"] .modal-close {
    right: auto;
    left: 10px;
}

[dir="rtl"] .modal-footer {
    justify-content: flex-start;
}

.small-link {
    display: block;
    margin-top: 8px;
    color: var(--profile-primary);
    font-size: 12px;
    text-decoration: none;
}

.small-link:hover {
    text-decoration: underline;
}

.gravatar-preview {
    display: flex;
    gap: 20px;
    margin: 20px 0;
    flex-wrap: wrap;
}

.preview-section {
    flex: 1;
    min-width: 200px;
    text-align: center;
}

img.sekaiplus-premium-avatar {
    overflow: hidden !important;
    border-radius: 50% !important;
    width: 100% !important;
    height: 100% !important;
    object-fit: cover !important;
}

/* زر اظهر المزيد */
.load-more-btn {
    display: block;
    margin: 20px auto;
    padding: 10px 25px;
    background-color: var(--profile-primary);
    color: #fff;
    border: none;
    border-radius: 50px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.load-more-btn:hover {
    background-color: #2980b9;
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15);
}

.load-more-btn i {
    margin-left: 8px;
}

/* تخفي العناصر الزائدة */
.hidden-item {
    display: none;
}
</style>
<?php if (function_exists('sekaiplus_is_premium_user') && sekaiplus_is_premium_user($author_id)): ?>
<style>
    /* تصحيح مشكلة الصورة الشخصية للعضو المميز */
    .premium-glow-avatar {
        overflow: hidden !important;
    }

    .premium-glow-avatar img {
        border-radius: 50% !important;
        width: 100% !important;
        height: 100% !important;
        object-fit: cover !important;
    }

    i.fas.fa-globe {
    margin-left: 0px !important;
    margin-right: 0px !important;
}

i.fab.fa-x-twitter {
    margin-left: 0px !important;
    margin-right: 0px !important;
}

a.social-icon.instagram-icon {
    margin-left: 0px !important;
    margin-right: 0px !important;
}

a.social-icon.telegram-icon {
    margin-left: 0px !important;
    margin-right: 0px !important;
}

a.social-icon.discord-icon {
    margin-left: 0px !important;
    margin-right: 0px !important;
}

.premium-badge {
    background: linear-gradient(135deg, #ffd700, #ffcc00);
    color: #fff;
    animation: badgeGlow 3s ease-in-out infinite;
}

.elite-badge {
    background: linear-gradient(135deg, #ff9800, #f57c00);
    color: #fff;
    animation: badgeShine 4s ease infinite;
}

.translator-badge {
    background: linear-gradient(135deg, #00b09b, #96c93d);
    color: #fff;
}

.active-translator-badge {
    background: linear-gradient(135deg, #4361ee, #3f5efb, #7b4397);
    background-size: 200% 200%;
    color: #fff;
    animation: badgeShine 4s ease infinite, badgePulse 2s infinite;
    border: 1px solid rgba(255,255,255,0.2);
    box-shadow: 0 3px 10px rgba(67, 97, 238, 0.3);
}

.active-commenter-badge {
    background: linear-gradient(135deg, #43cea2, #185a9d);
    color: #fff;
    animation: badgeShine 5s ease infinite;
}
.comment-card.admin-blur {
    position: relative !important;
}

.admin-comments-container {
    position: relative !important;
}

.admin-comments-container .blur-content {
    filter: blur(6px) !important;
    opacity: 0.7 !important;
}

.admin-comments-container .admin-blur-overlay {
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    width: 100% !important;
    height: 100% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-size: 32px !important;
    font-weight: bold !important;
    color: white !important;
    background: rgba(0,0,0,0.7) !important;
    z-index: 999 !important;
    pointer-events: none !important;
}

</style>
<?php endif; ?>
<div class="profile-modern">
    <div class="container">
        <!-- Profile Header - Redesigned with modern layered approach -->
        <div class="profile-header-modern">
            <div class="profile-cover-modern" <?php
                // إضافة صورة الغلاف من Gravatar
                $email_hash = md5(strtolower(trim($author->user_email)));
                $header_url = "https://2.gravatar.com/userimage/" . substr($email_hash, 0, 16) . "?size=1200";
                echo 'style="background-image: url(\''.esc_url($header_url).'\')";';
            ?>></div>
            <div class="profile-header-overlay"></div>

            <div class="profile-info-modern">
                <div class="avatar-container">
                    <?php echo get_avatar($author_id, 150, '', esc_attr($author->display_name), array('class' => 'profile-avatar')); ?>
                    <?php if ($role === 'admin'): ?>
                        <div class="admin-badge-overlay"><i class="fas fa-crown"></i></div>
                    <?php endif; ?>

                    <?php if (get_current_user_id() == $author_id): ?>
                        <a href="#" class="avatar-edit-btn" title="تغيير الصورة الشخصية">
                            <i class="fas fa-camera"></i>
                        </a>
                    <?php endif; ?>
                </div>

                <div class="profile-details-modern">
                    <div class="profile-name-section">
                        <h1 class="profile-name">
                            <?php echo esc_html($author->display_name); ?>
                            <?php if (get_current_user_id() == $author_id): ?>
                                <a href="#" class="edit-name-btn" title="تعديل الاسم"><i class="fas fa-pencil-alt"></i></a>
                            <?php endif; ?>
                        </h1>

                        <div class="profile-meta">
                            <span class="join-date"><i class="far fa-calendar-alt"></i> عضو منذ: <?php echo date_i18n(get_option('date_format'), strtotime($author->user_registered)); ?></span>
                        </div>
                    </div>

                    <div class="profile-social">
                        <?php
                        // Get social media profiles
                        $twitter = get_user_meta($author_id, 'twitter', true);
                        $instagram = get_user_meta($author_id, 'instagram', true);
                        $discord = get_user_meta($author_id, 'discord', true);
                        $telegram = get_user_meta($author_id, 'telegram', true);
                        $website = get_user_meta($author_id, 'user_url', true);

                        if (empty($website) && !empty($author->user_url)) {
                            $website = $author->user_url;
                        }

                        if (!empty($website) || !empty($twitter) || !empty($instagram) || !empty($discord) || !empty($telegram)):
                        ?>
                        <div class="social-icons">
                            <?php if (!empty($website)): ?>
                                <a href="<?php echo esc_url($website); ?>" class="social-icon website-icon" target="_blank" rel="noopener" title="الموقع الشخصي">
                                    <i class="fas fa-globe"></i>
                                </a>
                            <?php endif; ?>

                            <?php if (!empty($twitter)): ?>
                                <a href="https://twitter.com/<?php echo esc_attr($twitter); ?>" class="social-icon twitter-icon" target="_blank" rel="noopener" title="منصة X">
                                    <i class="fab fa-x-twitter"></i>
                                </a>
                            <?php endif; ?>

                            <?php if (!empty($instagram)): ?>
                                <a href="https://instagram.com/<?php echo esc_attr($instagram); ?>" class="social-icon instagram-icon" target="_blank" rel="noopener" title="انستغرام">
                                    <i class="fab fa-instagram"></i>
                                </a>
                            <?php endif; ?>

                            <?php if (!empty($discord)): ?>
                                <a href="https://discord.com/users/<?php echo esc_attr($discord); ?>" class="social-icon discord-icon" target="_blank" rel="noopener" title="ديسكورد">
                                    <i class="fab fa-discord"></i>
                                </a>
                            <?php endif; ?>

                            <?php if (!empty($telegram)): ?>
                                <a href="https://t.me/<?php echo esc_attr($telegram); ?>" class="social-icon telegram-icon" target="_blank" rel="noopener" title="تيليغرام">
                                    <i class="fab fa-telegram-plane"></i>
                                </a>
                            <?php endif; ?>
                        </div>
                        <?php endif; ?>

                        <?php if (get_current_user_id() == $author_id): ?>
                            <a href="#" class="edit-social-btn">
                                <i class="fas fa-edit"></i> تعديل وسائل التواصل
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <?php
        // عرض معلومات الفريق إذا كان العضو منضماً لأي فريق
        if (is_plugin_active('team-system/team-system.php') || class_exists('Team_System_Admin')) {
            global $wpdb;

            // جلب معلومات الفريق الذي ينتمي إليه العضو
            $user_team = $wpdb->get_row($wpdb->prepare(
                "SELECT t.*, tm.role, tm.joined_at
                 FROM {$wpdb->prefix}teams t
                 INNER JOIN {$wpdb->prefix}team_members tm ON t.id = tm.team_id
                 WHERE tm.user_id = %d
                 LIMIT 1",
                $author_id
            ));

            if ($user_team) {
                // دالة للحصول على أسماء الأدوار
                $team_roles = array(
                    'leader' => 'قائد الفريق',
                    'translator' => 'مترجم',
                    'editor' => 'مدقق لغوي',
                    'reviewer' => 'مراجع جودة',
                    'designer' => 'مصمم رسوم',
                    'member' => 'عضو'
                );
                $role_name = isset($team_roles[$user_team->role]) ? $team_roles[$user_team->role] : $user_team->role;
                $is_leader = ($user_team->role === 'leader');
        ?>
        <!-- Team Section - Modern & Professional -->
        <div class="team-section-modern animate-fade-in">
            <div class="team-card-modern">
                <div class="team-header-modern">
                    <div class="team-icon-container">
                        <?php if (!empty($user_team->logo_url)): ?>
                            <img src="<?php echo esc_url($user_team->logo_url); ?>" alt="<?php echo esc_attr($user_team->name); ?>" class="team-logo">
                        <?php else: ?>
                            <div class="team-icon-default">
                                <i class="fas fa-users"></i>
                            </div>
                        <?php endif; ?>
                        <?php if ($is_leader): ?>
                            <div class="leader-badge-overlay">
                                <i class="fas fa-crown"></i>
                            </div>
                        <?php endif; ?>
                    </div>

                    <div class="team-info-modern">
                        <h3 class="team-title">
                            <i class="fas fa-users"></i>
                            عضو في فريق
                        </h3>
                        <div class="team-name">
                            <a href="<?php echo home_url('/team/' . $user_team->slug); ?>" class="team-link">
                                <?php echo esc_html($user_team->name); ?>
                            </a>
                        </div>
                        <div class="team-role-badge <?php echo $is_leader ? 'leader-role' : 'member-role'; ?>">
                            <?php if ($is_leader): ?>
                                <i class="fas fa-crown"></i>
                            <?php else: ?>
                                <i class="fas fa-user"></i>
                            <?php endif; ?>
                            <?php echo esc_html($role_name); ?>
                        </div>
                        <div class="team-joined-date">
                            <i class="far fa-calendar-alt"></i>
                            انضم في <?php echo date_i18n('j F Y', strtotime($user_team->joined_at)); ?>
                        </div>
                    </div>
                </div>

                <div class="team-actions-modern">
                    <a href="<?php echo home_url('/team/' . $user_team->slug); ?>" class="btn-visit-team">
                        <i class="fas fa-external-link-alt"></i>
                        زيارة صفحة الفريق
                    </a>
                </div>
            </div>
        </div>
        <?php
            }
        }
        ?>

        <!-- Statistics Cards -->
        <div class="profile-stats-modern">
            <?php if (!$is_subscriber_only): ?>
            <div class="stat-card animate-fade-in">
                <i class="fas fa-book stat-icon"></i>
                <div class="stat-value"><?php echo $novels_count; ?></div>
                <div class="stat-label">الروايات المنشورة</div>
            </div>
            <div class="stat-card animate-fade-in">
                <i class="fas fa-file-alt stat-icon"></i>
                <div class="stat-value"><?php echo $chapters_count; ?></div>
                <div class="stat-label">الفصول المترجمة</div>
            </div>
            <?php endif; ?>
            <div class="stat-card animate-fade-in">
                <i class="fas fa-comments stat-icon"></i>
                <div class="stat-value"><?php echo $comments_count; ?></div>
                <div class="stat-label">التعليقات</div>
            </div>
        </div>

        <!-- Badges Section - Modern & Professional -->
        <div class="profile-badges-section animate-fade-in">
            <h3 class="badges-title"><i class="fas fa-award"></i> الشارات والإنجازات</h3>
            <div class="profile-badges">
                <?php
                // التحقق مما إذا كانت إضافة sekaiplus-premium نشطة
                $is_premium_plugin_active = false;
                if (function_exists('is_plugin_active')) {
                    $is_premium_plugin_active = is_plugin_active('sekaiplus-premium/sekaiplus-premium.php');
                } else {
                    $is_premium_plugin_active = class_exists('SekaiPlus_Premium');
                }

                // عرض الشارات فقط إذا كانت الإضافة غير نشطة
                if (!$is_premium_plugin_active):
                ?>
                    <?php if ($role === 'admin'): ?>
                        <span class="badge admin-badge"><i class="fas fa-shield-alt"></i> إدارة</span>
                    <?php elseif ($role === 'translator'): ?>
                        <span class="badge translator-badge"><i class="fas fa-language"></i> مترجم</span>
                    <?php else: ?>
                        <span class="badge reader-badge"><i class="fas fa-book-reader"></i> قارئ</span>
                    <?php endif; ?>
                <?php endif; ?>

                <?php
                    // Display elite badge if function exists
                    if (function_exists('display_elite_badge')) {
                        display_elite_badge($author_id);
                    }
                ?>

                <?php if (function_exists('sekaiplus_is_premium_user') && sekaiplus_is_premium_user($author_id)): ?>
                    <span class="badge premium-badge"><i class="fas fa-gem"></i>مميز</span>
                <?php endif; ?>

                <?php if ($novels_count > 0): ?>
                    <span class="badge translator-badge"><i class="fas fa-pen-fancy"></i> ناشر روايات</span>
                <?php endif; ?>

                <?php if ($chapters_count > 10): ?>
                    <span class="badge active-translator-badge"><i class="fas fa-star"></i> مترجم نشط</span>
                <?php endif; ?>

                <?php if ($comments_count > 50): ?>
                    <span class="badge active-commenter-badge"><i class="fas fa-comments"></i> معلق نشط</span>
                <?php endif; ?>
            </div>
        </div>

        <!-- Content Tabs -->
        <div class="profile-tabs-modern">
            <nav class="tabs-nav-modern">
                <?php if (!$is_subscriber_only): ?>
                <a href="#" class="tab-link-modern active" data-tab="novels">
                    <i class="fas fa-book"></i> الروايات
                </a>
                <a href="#" class="tab-link-modern" data-tab="chapters">
                    <i class="fas fa-file-alt"></i> الفصول
                </a>
                <?php endif; ?>
                <a href="#" class="tab-link-modern<?php echo ($is_subscriber_only ? ' active' : ''); ?>" data-tab="comments">
                    <i class="fas fa-comments"></i> التعليقات
                </a>
                <a href="#" class="tab-link-modern" data-tab="favorites">
                    <i class="fas fa-star"></i> المفضلة
                </a>
                <a href="#" class="tab-link-modern" data-tab="about">
                    <i class="fas fa-user"></i> نبذة
                </a>
            </nav>

            <div class="tab-content-modern">
                <?php if (!$is_subscriber_only): ?>
                <!-- Novels Tab -->
                <div id="novels" class="tab-pane-modern active">
                    <div class="novel-grid">
                        <?php
                        $novels = get_posts(array(
                            'post_type' => 'novel',
                            'author' => $author_id,
                            'posts_per_page' => -1
                        ));

                        if (!empty($novels)):
                            $total_novels = count($novels);
                            $novels_to_show = 12; // عدد الروايات التي ستظهر في البداية

                            foreach ($novels as $index => $novel):
                                $cover = get_the_post_thumbnail_url($novel->ID) ?: get_template_directory_uri() . '/images/default-novel.jpg';
                                $hidden_class = $index >= $novels_to_show ? 'hidden-item' : '';
                        ?>
                        <div class="novel-card animate-fade-in <?php echo $hidden_class; ?>" data-item-index="<?php echo $index; ?>">
                            <div class="novel-card-cover">
                                <img src="<?php echo esc_url($cover); ?>" alt="<?php echo esc_attr($novel->post_title); ?>" />
                                <div class="novel-card-overlay">
                                    <h3 class="content-card-title" style="margin: 0; font-size: 14px; color: #fff; text-align: center;">
                                        <a href="<?php echo get_permalink($novel->ID); ?>" style="color: #fff;"><?php echo esc_html($novel->post_title); ?></a>
                                    </h3>
                                    <div class="content-card-meta" style="color: rgba(255,255,255,0.8); font-size: 12px; text-align: center; margin-top: 5px;">
                                        <i class="far fa-calendar"></i> <?php echo get_the_date('', $novel->ID); ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php
                            endforeach;

                            // إضافة زر "اظهر المزيد" إذا كان عدد الروايات أكبر من 12
                            if ($total_novels > $novels_to_show):
                        ?>
                            <button class="load-more-btn" data-tab="novels" data-total="<?php echo $total_novels; ?>" data-increment="12">
                                اظهر المزيد <i class="fas fa-chevron-down"></i>
                            </button>
                        <?php
                            endif;
                        else:
                        ?>
                            <div class="no-content-message">
                                <i class="fas fa-book"></i>
                                <p>لا توجد روايات منشورة حتى الآن</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Chapters Tab -->
                <div id="chapters" class="tab-pane-modern">
                    <div class="content-grid">
                        <?php
                        $chapters = get_posts(array(
                            'post_type' => 'chapter',
                            'author' => $author_id,
                            'posts_per_page' => -1
                        ));

                        if (!empty($chapters)):
                            $total_chapters = count($chapters);
                            $chapters_to_show = 12; // عدد الفصول التي ستظهر في البداية

                            foreach ($chapters as $index => $chapter):
                                $novel_id = get_post_meta($chapter->ID, '_novel_id', true) ?: wp_get_post_parent_id($chapter->ID);
                                $novel_title = get_the_title($novel_id);
                                $chapter_number = get_post_meta($chapter->ID, '_chapter_number', true);
                                $hidden_class = $index >= $chapters_to_show ? 'hidden-item' : '';
                        ?>
                        <div class="content-card animate-fade-in <?php echo $hidden_class; ?>" data-item-index="<?php echo $index; ?>">
                            <div class="content-card-body">
                                <h3 class="content-card-title">
                                    <a href="<?php echo get_permalink($chapter->ID); ?>">
                                        <?php echo esc_html($novel_title); ?> - الفصل <?php echo esc_html($chapter_number); ?>
                                    </a>
                                </h3>
                                <div class="content-card-meta">
                                    <div><i class="fas fa-book"></i> <?php echo esc_html($novel_title); ?></div>
                                    <div><i class="far fa-calendar"></i> <?php echo get_the_date('', $chapter->ID); ?></div>
                                </div>
                            </div>
                        </div>
                        <?php
                            endforeach;

                            // إضافة زر "اظهر المزيد" إذا كان عدد الفصول أكبر من 12
                            if ($total_chapters > $chapters_to_show):
                        ?>
                            <button class="load-more-btn" data-tab="chapters" data-total="<?php echo $total_chapters; ?>" data-increment="12">
                                اظهر المزيد <i class="fas fa-chevron-down"></i>
                            </button>
                        <?php
                            endif;
                        else:
                        ?>
                            <div class="no-content-message">
                                <i class="fas fa-file-alt"></i>
                                <p>لا توجد فصول مترجمة حتى الآن</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endif; ?>
                <!-- Comments Tab -->
<div id="comments" class="tab-pane-modern<?php echo ($is_subscriber_only ? ' active' : ''); ?>">
    <div class="comments-list">
        <?php
        $comments = get_comments(array(
            'user_id' => $author_id,
            'status' => 'approve',
            'order' => 'DESC'
        ));

        // يمكنك إضافة المزيد من معرفات المسؤولين هنا
        $admin_ids = [1]; // ضع هنا معرفات حسابات الإدارة

        // تحقق إذا كان المستخدم هو من الإدارة
        $is_admin_author = in_array($author_id, $admin_ids);
        ?>

        <?php if ($is_admin_author): ?>
        <div class="admin-comments-container">
            <div class="admin-blur-overlay">خاص</div>
            <div class="blur-content">
        <?php endif; ?>

        <?php if (!empty($comments)): ?>
            <?php foreach ($comments as $comment):
                $post = get_post($comment->comment_post_ID);
            ?>
                <div class="comment-card animate-fade-in">
                    <div class="comment-header">
                        <div class="comment-novel">
                            <a href="<?php echo get_permalink($post->ID); ?>"><?php echo esc_html($post->post_title); ?></a>
                        </div>
                        <div class="comment-date">
                            <i class="far fa-clock"></i> <?php echo get_comment_date('', $comment->comment_ID); ?>
                        </div>
                    </div>
                    <div class="comment-content">
                        <?php echo wp_kses_post($comment->comment_content); ?>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php else: ?>
            <div class="no-content-message">
                <i class="fas fa-comments"></i>
                <p>لا توجد تعليقات حتى الآن</p>
            </div>
        <?php endif; ?>

        <?php if ($is_admin_author): ?>
            </div><!-- /.blur-content -->
        </div><!-- /.admin-comments-container -->
        <?php endif; ?>

    </div>
</div>
                <!-- Favorites Tab -->
                <div id="favorites" class="tab-pane-modern">
                    <div class="novel-grid">
                        <?php if (!empty($fav_novels)): ?>
                            <?php
                            $total_favorites = count($fav_novels);
                            $favorites_to_show = 12; // عدد الروايات المفضلة التي ستظهر في البداية

                            foreach ($fav_novels as $index => $novel):
                                $cover = get_the_post_thumbnail_url($novel->ID) ?: get_template_directory_uri() . '/images/default-novel.jpg';
                                $hidden_class = $index >= $favorites_to_show ? 'hidden-item' : '';
                            ?>
                            <div class="novel-card animate-fade-in <?php echo $hidden_class; ?>" data-item-index="<?php echo $index; ?>">
                                <div class="novel-card-cover">
                                    <img src="<?php echo esc_url($cover); ?>" alt="<?php echo esc_attr($novel->post_title); ?>" />
                                    <div class="novel-card-overlay">
                                        <h3 class="content-card-title" style="margin: 0; font-size: 14px; color: #fff; text-align: center;">
                                            <a href="<?php echo get_permalink($novel->ID); ?>" style="color: #fff;"><?php echo esc_html($novel->post_title); ?></a>
                                        </h3>
                                    </div>
                                </div>
                            </div>
                            <?php
                            endforeach;

                            // إضافة زر "اظهر المزيد" إذا كان عدد الروايات المفضلة أكبر من 12
                            if ($total_favorites > $favorites_to_show):
                            ?>
                                <button class="load-more-btn" data-tab="favorites" data-total="<?php echo $total_favorites; ?>" data-increment="12">
                                    اظهر المزيد <i class="fas fa-chevron-down"></i>
                                </button>
                            <?php
                            endif;
                            ?>
                        <?php else: ?>
                            <div class="no-content-message">
                                <i class="fas fa-star"></i>
                                <p>لا توجد روايات مفضلة حتى الآن</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- About Tab -->
                <div id="about" class="tab-pane-modern">
                    <div class="about-section">
                        <div class="bio-card">
                            <h3><i class="fas fa-user-circle"></i> نبذة عن <?php echo esc_html($author->display_name); ?></h3>
                            <?php if (!empty($bio)): ?>
                                <div class="bio-content">
                                    <?php echo wpautop(wp_kses_post($bio)); ?>
                                </div>
                            <?php else: ?>
                                <p class="no-content">لا توجد معلومات إضافية عن هذا المستخدم</p>
                            <?php endif; ?>
                        </div>

                        <?php if ($website || $twitter || $discord): ?>
                        <div class="contact-card">
                            <h3><i class="fas fa-address-card"></i> معلومات الاتصال</h3>
                            <ul class="contact-list">
                                <?php if ($website): ?>
                                    <li>
                                        <i class="fas fa-globe"></i>
                                        <a href="<?php echo esc_url($website); ?>" target="_blank" rel="noopener">الموقع الشخصي</a>
                                    </li>
                                <?php endif; ?>
                                <?php if ($twitter): ?>
                                    <li>
                                        <i class="fab fa-twitter"></i>
                                        <a href="https://twitter.com/<?php echo esc_attr($twitter); ?>" target="_blank" rel="noopener">تويتر</a>
                                    </li>
                                <?php endif; ?>
                                <?php if ($discord): ?>
                                    <li>
                                        <i class="fab fa-discord"></i>
                                        <span><?php echo esc_html($discord); ?></span>
                                    </li>
                                <?php endif; ?>
                            </ul>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php if (get_current_user_id() == $author_id): ?>
<!-- Profile Image Modal -->
<div id="avatarModal" class="modal-backdrop">
    <div class="modal-container">
        <button class="modal-close" data-dismiss="modal" aria-label="أغلق"><i class="fas fa-times"></i></button>
        <h3 class="modal-title">تحديث صورة الملف الشخصي</h3>
        <div class="modal-body">
            <div class="gravatar-instructions">
                <p>لتحديث صورتك الشخصية وترويسة ملفك عبر Gravatar، اتبع الخطوات التالية:</p>

                <div class="gravatar-preview">
                    <div class="preview-section">
                        <h4>الصورة الحالية</h4>
                        <?php echo get_avatar($author->ID, 100, 'mystery', '', array('class' => 'preview-avatar')); ?>
                    </div>

                    <div class="preview-section">
                    <h4>الترويسة الحالية</h4>
                        <?php
    $email_hash = md5(strtolower(trim($author->user_email)));
    $profile_url = "https://www.gravatar.com/" . $email_hash;
    // استعلام لإحضار بيانات ملف Gravatar باستخدام API الخاص بهم
    $api_url = "https://www.gravatar.com/" . $email_hash . ".json";
    $header_url = "https://2.gravatar.com/userimage/" . substr($email_hash, 0, 16) . "?size=600";
?>
<div class="header-preview" style="background-image: url('<?php echo esc_url($header_url); ?>'); background-size: cover; width: 100%; height: 120px; border-radius: 6px; background-position: center;"></div>
<a href="<?php echo esc_url($profile_url); ?>" target="_blank" class="small-link">عرض الملف الشخصي في Gravatar</a>
                    </div>
                </div>

                <ol>
                    <li>قم بزيارة موقع <a href="https://gravatar.com" target="_blank">Gravatar.com</a></li>
                    <li>سجل الدخول باستخدام بريدك الإلكتروني:
                        <strong><?php echo esc_html($author->user_email); ?></strong>
                    </li>
                    <li>قم بتحميل أو تحديث صورتك الشخصية وترويستك</li>
                    <li>عد إلى هذه الصفحة واضغط F5 لتحديث الصفحة ورؤية التغييرات</li>
                </ol>

                <div class="gravatar-info">
                    <p><i class="fas fa-info-circle"></i> ملحوظة: يمكنك تخصيص صورتك وترويستك وإعداداتك الأخرى بالكامل من موقع Gravatar.</p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">إغلاق</button>
                <a href="https://gravatar.com" target="_blank" class="btn btn-primary">زيارة Gravatar</a>
            </div>
        </div>
    </div>
</div>

<!-- Display Name Modal -->
<div id="nameModal" class="modal-backdrop">
    <div class="modal-container">
        <button class="modal-close" data-dismiss="modal" aria-label="أغلق"><i class="fas fa-times"></i></button>
        <h3 class="modal-title">تعديل اسم العرض</h3>
        <div class="modal-body">
            <form id="name-form" method="post" action="<?php echo admin_url('admin-ajax.php'); ?>">
                <input type="hidden" name="action" value="update_display_name">
                <?php wp_nonce_field('update_display_name', 'name_nonce'); ?>

                <div class="form-group">
                    <label for="display_name">اسم العرض</label>
                    <input type="text" id="display_name" name="display_name" class="form-control" value="<?php echo esc_attr($author->display_name); ?>" required>
                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Social Media Modal -->
<div id="socialModal" class="modal-backdrop">
    <div class="modal-container">
        <button class="modal-close" data-dismiss="modal" aria-label="أغلق"><i class="fas fa-times"></i></button>
        <h3 class="modal-title">تعديل وسائل التواصل الاجتماعي</h3>
        <div class="modal-body">
            <form id="social-form" method="post" action="<?php echo admin_url('admin-ajax.php'); ?>">
                <input type="hidden" name="action" value="update_social_media">
                <?php wp_nonce_field('update_social_media', 'social_nonce'); ?>

                <div class="form-group">
                    <label for="website">الموقع الشخصي (URL كامل)</label>
                    <input type="url" id="website" name="website" class="form-control" value="<?php echo esc_attr($website); ?>">
                </div>

                <div class="form-group">
                    <label for="twitter">منصة X (اسم المستخدم فقط بدون @)</label>
                    <input type="text" id="twitter" name="twitter" class="form-control" value="<?php echo esc_attr($twitter); ?>">
                </div>

                <div class="form-group">
                    <label for="instagram">انستغرام (اسم المستخدم فقط)</label>
                    <input type="text" id="instagram" name="instagram" class="form-control" value="<?php echo esc_attr($instagram); ?>">
                </div>

                <div class="form-group">
                    <label for="discord">ديسكورد (اسم المستخدم كاملاً)</label>
                    <input type="text" id="discord" name="discord" class="form-control" value="<?php echo esc_attr($discord); ?>">
                </div>

                <div class="form-group">
                    <label for="telegram">تيليغرام (اسم المستخدم فقط بدون @)</label>
                    <input type="text" id="telegram" name="telegram" class="form-control" value="<?php echo esc_attr($telegram); ?>">
                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Bio Modal -->
<div id="bioModal" class="modal-backdrop">
    <div class="modal-container">
        <button class="modal-close" data-dismiss="modal" aria-label="أغلق"><i class="fas fa-times"></i></button>
        <h3 class="modal-title">تعديل النبذة الشخصية</h3>
        <div class="modal-body">
            <form id="bio-form" method="post" action="<?php echo admin_url('admin-ajax.php'); ?>">
                <input type="hidden" name="action" value="update_user_bio">
                <?php wp_nonce_field('update_user_bio', 'bio_nonce'); ?>

                <div class="form-group">
                    <label for="description">النبذة الشخصية</label>
                    <textarea id="description" name="description" class="form-control" rows="5"><?php echo esc_textarea($bio); ?></textarea>
                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ</button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php endif; ?>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Dark Mode Toggle
    const darkModeToggle = document.querySelector('.dark-mode-toggle');

    // Check for saved dark mode preference
    if (localStorage.getItem('darkMode') === 'true') {
        document.body.classList.add('dark-mode');
    }

    // Toggle dark mode
    if (darkModeToggle) {
        darkModeToggle.addEventListener('click', function() {
            document.body.classList.toggle('dark-mode');

            // Save preference to localStorage
            if (document.body.classList.contains('dark-mode')) {
                localStorage.setItem('darkMode', 'true');
            } else {
                localStorage.setItem('darkMode', 'false');
            }
        });
    }
    // Tab Navigation
    const tabLinks = document.querySelectorAll('.tab-link-modern');
    const tabPanes = document.querySelectorAll('.tab-pane-modern');

    tabLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const tabId = this.getAttribute('data-tab');

            // Remove active class from all tabs and panes
            tabLinks.forEach(tab => tab.classList.remove('active'));
            tabPanes.forEach(pane => pane.classList.remove('active'));

            // Add active class to current tab and pane
            this.classList.add('active');
            document.getElementById(tabId).classList.add('active');
        });
    });

    // زر اظهر المزيد
    const loadMoreButtons = document.querySelectorAll('.load-more-btn');

    loadMoreButtons.forEach(button => {
        button.addEventListener('click', function() {
            const tabId = this.getAttribute('data-tab');
            const total = parseInt(this.getAttribute('data-total'));
            const increment = parseInt(this.getAttribute('data-increment'));
            const container = document.getElementById(tabId);

            // الحصول على العناصر المخفية
            const hiddenItems = container.querySelectorAll('.hidden-item');
            let count = 0;

            // إظهار العناصر المخفية بالعدد المحدد
            hiddenItems.forEach(item => {
                if (count < increment) {
                    item.classList.remove('hidden-item');
                    count++;
                }
            });

            // إخفاء الزر إذا تم إظهار جميع العناصر
            if (hiddenItems.length <= increment) {
                this.style.display = 'none';
            }
        });
    });

    // Animation for cards, badges and other elements
    function animateOnScroll() {
        const animateElements = document.querySelectorAll('.animate-fade-in, .badge, .stat-card, .content-card, .comment-card');

        animateElements.forEach((element, index) => {
            const elementPosition = element.getBoundingClientRect().top;
            const screenPosition = window.innerHeight;

            if (elementPosition < screenPosition) {
                // Add staggered animation delay based on element type and position
                if (element.classList.contains('badge')) {
                    element.style.animationDelay = `${0.1 + (index * 0.1)}s`;
                } else if (element.classList.contains('stat-card')) {
                    element.style.animationDelay = `${0.2 + (index * 0.15)}s`;
                } else {
                    element.style.animationDelay = `${0.1 + (Math.random() * 0.3)}s`;
                }

                element.classList.add('animate-fade-in');
                element.style.opacity = '1';
                element.style.animationPlayState = 'running';
            }
        });
    }

    window.addEventListener('scroll', animateOnScroll);
    animateOnScroll(); // Run once on page load

    <?php if (get_current_user_id() == $author_id): ?>
    // Modal functionality
    const modals = document.querySelectorAll('.modal-backdrop');
    const modalOpeners = {
        'avatar': document.querySelector('.avatar-edit-btn'),
        'name': document.querySelector('.edit-name-btn'),
        'social': document.querySelector('.edit-social-btn'),
        'bio': document.querySelector('.edit-bio-btn')
    };

    // Open modal
    for (const [key, opener] of Object.entries(modalOpeners)) {
        if (opener) {
            opener.addEventListener('click', function(e) {
                e.preventDefault();
                document.getElementById(`${key}Modal`).style.display = 'flex';
            });
        }
    }

    // Close modal
    document.querySelectorAll('[data-dismiss="modal"]').forEach(button => {
        button.addEventListener('click', function() {
            this.closest('.modal-backdrop').style.display = 'none';
        });
    });

    // Close modal when clicking outside
    modals.forEach(modal => {
        modal.addEventListener('click', function(e) {
            if (e.target === modal) {
                modal.style.display = 'none';
            }
        });
    });

    // Add edit bio button
    const bioCard = document.querySelector('.bio-card');
    if (bioCard) {
        const bioHeader = bioCard.querySelector('h3');
        if (bioHeader) {
            const editBioBtn = document.createElement('a');
            editBioBtn.href = '#';
            editBioBtn.className = 'edit-bio-btn';
            editBioBtn.innerHTML = '<i class="fas fa-pencil-alt"></i>';
            editBioBtn.style.marginRight = 'auto';
            editBioBtn.style.fontSize = '16px';
            editBioBtn.style.color = 'var(--profile-primary)';

            bioHeader.appendChild(editBioBtn);

            editBioBtn.addEventListener('click', function(e) {
                e.preventDefault();
                document.getElementById('bioModal').style.display = 'flex';
            });
        }
    }

    // Form submissions using AJAX
    document.querySelectorAll('#name-form, #social-form, #bio-form').forEach(form => {
        form.addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);

            fetch(this.getAttribute('action'), {
                method: 'POST',
                body: formData,
                credentials: 'same-origin'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Reload page to see changes
                    window.location.reload();
                } else {
                    alert('حدث خطأ. يرجى المحاولة مرة أخرى.');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ. يرجى المحاولة مرة أخرى.');
            });
        });
    });
    <?php endif; ?>

    // Support RTL for design elements
    document.querySelectorAll('.badge i, .social-icon i, .profile-meta i, .stat-icon').forEach(icon => {
        if (document.dir === 'rtl' || document.documentElement.lang === 'ar') {
            icon.style.marginLeft = '';
            icon.style.marginRight = '';
        }
    });
});

document.addEventListener('DOMContentLoaded', function() {
    // اضافة تغبيش مباشر لقسم تعليقات الإدارة
    document.querySelectorAll('.admin-comments-container').forEach(function(container) {
        // تأكيد من تطبيق التغييرات CSS
        container.style.position = 'relative';
        container.style.overflow = 'hidden';

        // التأكد من وجود طبقة blur-content وتطبيق التغبيش
        let blurContent = container.querySelector('.blur-content');
        if (blurContent) {
            blurContent.style.filter = 'blur(6px)';
            blurContent.style.opacity = '0.7';
        }

        // التأكد من وجود طبقة الـ overlay
        let overlay = container.querySelector('.admin-blur-overlay');
        if (overlay) {
            overlay.style.position = 'absolute';
            overlay.style.top = '0';
            overlay.style.left = '0';
            overlay.style.width = '100%';
            overlay.style.height = '100%';
            overlay.style.display = 'flex';
            overlay.style.alignItems = 'center';
            overlay.style.justifyContent = 'center';
            overlay.style.background = 'rgba(0,0,0,0.7)';
            overlay.style.color = 'white';
            overlay.style.fontSize = '32px';
            overlay.style.fontWeight = 'bold';
            overlay.style.zIndex = '999';
        }
    });
});
</script>

<?php get_footer(); ?>