<?php get_header(); ?>

<!-- تحقق مما إذا كان الوضع الداكن مفعل -->
<?php $dark_mode = isset($_COOKIE['dark_mode']) && $_COOKIE['dark_mode'] === 'true'; ?>

<style>
/* تنسيق سلايدر الروايات المميزة */
.featured-novels-slider {
    height: 100%;
    width: 100%;
    background-color: #1a1a1a;
    border-radius: 10px;
    overflow: hidden;
}

.featured-novels-slider .swiper-slide {
    position: relative;
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: row;
}

.featured-novel-image {
    width: 40%;
    height: 100%;
    background-size: cover;
    background-position: center;
    position: absolute;
    top: 0;
    right: 0;
    transition: transform 0.5s ease;
}

.featured-novel-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to right, rgba(26,26,26,1) 0%, rgba(26,26,26,0.9) 30%, rgba(26,26,26,0.5) 60%, rgba(26,26,26,0.2) 100%);
}

.featured-novel-title-container {
    z-index: 10;
    text-align: right;
    width: 60% !important;
    right: auto !important;
    left: 0 !important;
    top: 0 !important;
    bottom: auto !important;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 2rem !important;
}

.featured-novel-title {
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
    font-weight: bold;
    font-size: 2rem;
    margin-bottom: 1rem !important;
}

.novel-info {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
}

.novel-info-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #e0e0e0;
}

.novel-rating {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.rating-star {
    color: gold;
    font-size: 1.5rem;
}

.rating-value {
    background-color: gold;
    color: #000;
    font-weight: bold;
    padding: 0.25rem 0.5rem;
    border-radius: 50%;
    width: 2.5rem;
    height: 2.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
}

.novel-genres {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
}

.novel-genre {
    background-color: rgba(255,255,255,0.1);
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    color: #fff;
}

.swiper-pagination-bullet {
    width: 10px;
    height: 10px;
    background-color: #fff;
    opacity: 0.5;
}

.swiper-pagination-bullet-active {
    opacity: 1;
    background-color: var(--bs-primary);
}

.swiper-pagination {
    position: absolute;
    bottom: 20px !important;
    z-index: 20;
}

.hero-image-container {
    min-height: 400px;
}
</style>

<div class="container-fluid px-md-4 py-5 <?php echo $dark_mode ? 'dark-theme' : 'light-theme'; ?>">
    <!-- Hero Section -->
    <section class="hero-section mb-5 position-relative">
        <div class="hero-container">
            <div class="row g-0">
                <!-- Left Content - Image Side -->
                <div class="col-lg-6 hero-image-container position-relative overflow-hidden">
                    <div class="swiper featured-novels-slider">
                        <div class="swiper-wrapper">
                            <?php
                            // Get featured novels
                            $featured_novels = new WP_Query(array(
                                'post_type' => 'novel',
                                'posts_per_page' => 5,
                                'meta_query' => array(
                                    array(
                                        'key' => '_featured_novel',
                                        'value' => '1',
                                        'compare' => '='
                                    )
                                )
                            ));

                            // Fallback if no featured novels are set
                            if ($featured_novels->post_count == 0) {
                                $featured_novels = new WP_Query(array(
                                    'post_type' => 'novel',
                                    'posts_per_page' => 5,
                                    'orderby' => 'date',
                                    'order' => 'DESC'
                                ));
                            }

                            if ($featured_novels->have_posts()) :
                                while ($featured_novels->have_posts()) : $featured_novels->the_post();
                                    // الحصول على التصنيفات
                                    $genres = get_the_terms(get_the_ID(), 'novel_genre');
                                    
                                    // الحصول على التقييمات
                                    $user_ratings = get_post_meta(get_the_ID(), '_user_ratings', true);
                                    $rating_count = is_array($user_ratings) ? count($user_ratings) : 0;
                                    $average_rating = 0;
                                    
                                    if ($rating_count > 0) {
                                        $total_rating = 0;
                                        foreach ($user_ratings as $rating) {
                                            $total_rating += $rating['rating'];
                                        }
                                        $average_rating = round($total_rating / $rating_count, 1);
                                    }
                                    
                                    // الحصول على سنة النشر
                                    $year = get_post_meta(get_the_ID(), '_novel_year', true);
                                    // الحصول على الحالة
                                    $status = get_post_meta(get_the_ID(), '_novel_status', true);
                                    // الحصول على نبذة مختصرة
                                    $excerpt = get_the_excerpt();
                            ?>
                            <div class="swiper-slide">
                                <?php if (has_post_thumbnail()) : ?>
                                    <div class="featured-novel-image" style="background-image: url('<?php echo esc_url(get_the_post_thumbnail_url(get_the_ID(), 'full')); ?>');"></div>
                                <?php else : ?>
                                    <div class="featured-novel-image" style="background-color: #f8f9fa;"></div>
                                <?php endif; ?>
                                <div class="featured-novel-overlay"></div>
                                <div class="featured-novel-title-container position-absolute p-4">
                                    <h3 class="featured-novel-title text-white mb-2"><?php the_title(); ?></h3>
                                    
                                    <!-- معلومات الرواية -->
                                    <div class="novel-rating">
                                        <div class="rating-value"><?php echo number_format($average_rating, 1); ?></div>
                                        <div class="rating-star"><i class="fas fa-star"></i></div>
                                    </div>
                                    
                                    <?php if (!empty($excerpt)) : ?>
                                    <div class="novel-excerpt text-white-50 mb-3">
                                        <?php echo wp_trim_words($excerpt, 20, '...'); ?>
                                    </div>
                                    <?php endif; ?>
                                    
                                    <div class="novel-info">
                                        <?php if (!empty($year)) : ?>
                                        <div class="novel-info-item">
                                            <i class="fas fa-calendar-alt"></i>
                                            <span><?php echo esc_html($year); ?></span>
                                        </div>
                                        <?php endif; ?>
                                        
                                        <?php if (!empty($status)) : ?>
                                        <div class="novel-info-item">
                                            <i class="fas fa-info-circle"></i>
                                            <span>الحالة: <?php echo esc_html($status); ?></span>
                                        </div>
                                        <?php endif; ?>
                                        
                                        <?php 
                                        $novel_type = get_post_meta(get_the_ID(), '_novel_type', true);
                                        if ($novel_type) : 
                                        ?>
                                        <div class="novel-info-item">
                                            <i class="fas fa-book"></i>
                                            <span>نوع: <?php echo $novel_type === 'light_novel' ? 'رواية خفيفة' : 'رواية ويب'; ?></span>
                                        </div>
                                        <?php else: ?>
                                        <div class="novel-info-item">
                                            <i class="fas fa-book"></i>
                                            <span>نوع: رواية ويب</span>
                                        </div>
                                        <?php endif; ?>
                                        
                                        <?php if ($genres && !is_wp_error($genres)) : ?>
                                        <div class="novel-genres">
                                            <?php foreach ($genres as $genre) : ?>
                                                <span class="novel-genre"><?php echo esc_html($genre->name); ?></span>
                                            <?php endforeach; ?>
                                        </div>
                                        <?php endif; ?>
                                        
                                        <a href="<?php the_permalink(); ?>" class="btn btn-sm btn-light rounded-pill px-3 shadow-sm">
                                            <i class="fas fa-eye me-1"></i>
                                            عرض الرواية
                                        </a>
                                    </div>
                                </div>
                            </div>
                            <?php
                                endwhile;
                                wp_reset_postdata();
                            else:
                            ?>
                            <div class="swiper-slide">
                                <div class="featured-novel-image" style="background-color: #f8f9fa;"></div>
                                <div class="featured-novel-overlay"></div>
                                <div class="featured-novel-title-container position-absolute p-4">
                                    <h3 class="featured-novel-title text-white mb-2">لا توجد روايات مميزة</h3>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>
                        <div class="swiper-pagination"></div>
                    </div>
                </div>
                
                <!-- Right Content - Text and Search -->
                <div class="col-lg-6 hero-content-container d-flex align-items-center justify-content-center">
                    <div class="hero-content text-center text-lg-start px-4 px-lg-5 py-5">
                        <h1 class="display-4 fw-bold mb-4 text-gradient hero-title">اكتشف عالم الروايات اليابانية</h1>
                        <p class="lead mb-4 opacity-85 hero-subtitle">استمتع بمكتبة متكاملة من أفضل الروايات الخفيفة مع ترجمات احترافية ومجتمع من القراء المتحمسين</p>
                        
                        <!-- Search Box -->
                        <div class="search-box-container mb-4">
                            <form class="hero-search-form" id="quickSearchForm">
                                <div class="search-input-wrapper position-relative">
                                    <input type="text" class="form-control form-control-lg rounded-pill px-4 py-3 shadow-sm" 
                                           id="quickSearch" placeholder="البحث عن رواية أو مترجم..." autocomplete="off">
                                    <button type="submit" class="btn btn-search position-absolute end-0 top-50 translate-middle-y me-2">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                                <div id="searchResults" class="quick-search-results shadow-lg rounded-4"></div>
                            </form>
                        </div>
                        
                        <!-- Quick Stats -->
                        <div class="hero-stats d-flex flex-wrap justify-content-center justify-content-lg-start gap-3 mt-4">
                            <?php
                            $stats = array(
                                array(
                                    'count' => wp_count_posts('novel')->publish,
                                    'label' => 'روايات متوفرة',
                                    'icon' => 'fa-book'
                                ),
                                array(
                                    'count' => wp_count_posts('chapter')->publish,
                                    'label' => 'فصل منشور',
                                    'icon' => 'fa-file-alt'
                                ),
                                array(
                                    'count' => count_users()['total_users'],
                                    'label' => 'مستخدم مسجل',
                                    'icon' => 'fa-users'
                                )
                            );

                            foreach ($stats as $stat) :
                            ?>
                                <div class="stat-pill py-2 px-3 rounded-pill d-inline-flex align-items-center gap-2">
                                    <i class="fas <?php echo $stat['icon']; ?>"></i>
                                    <span class="fw-bold"><?php echo number_format($stat['count']); ?></span>
                                    <span class="small"><?php echo $stat['label']; ?></span>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- أحدث الروايات -->
    <section class="latest-novels-section mb-5">
        <div class="section-header d-flex justify-content-between align-items-center mb-4">
            <div class="d-flex align-items-center">
                <div class="section-icon rounded-circle p-3 me-3">
                    <i class="fas fa-star fs-4"></i>
                </div>
                <h2 class="section-title h3 mb-0">أحدث الروايات</h2>
            </div>
            <a href="<?php echo home_url('/library/'); ?>" class="btn btn-sm btn-outline-primary rounded-pill px-3">
                عرض المزيد
                <i class="fas fa-arrow-left ms-1"></i>
            </a>
        </div>

        <div class="row row-cols-1 row-cols-sm-2 row-cols-md-3 row-cols-lg-4 row-cols-xl-6 g-4">
            <?php
            $latest_novels = new WP_Query(array(
                'post_type' => 'novel',
                'posts_per_page' => get_theme_mod('sekaiplus_latest_novels_count', 6),
                'orderby' => 'date',
                'order' => 'DESC'
            ));

            while ($latest_novels->have_posts()) : $latest_novels->the_post();
                $rating = sekaiplus_get_novel_rating(get_the_ID());
                $genres = get_the_terms(get_the_ID(), 'genre');
                $chapter_count = sekaiplus_get_chapter_count(get_the_ID());
                $excerpt = wp_trim_words(get_the_excerpt(), 20, '...');
            ?>
                <div class="col">
                    <article class="novel-card h-100 position-relative overflow-hidden" data-novel-id="<?php echo get_the_ID(); ?>">
                        <div class="card border-0 rounded-4 shadow-sm h-100 novel-card-inner">
                            <div class="novel-cover-link position-relative overflow-hidden rounded-4">
                                <?php if (has_post_thumbnail()) : ?>
                                    <?php the_post_thumbnail('medium', array('class' => 'novel-cover img-fluid')); ?>
                                <?php else : ?>
                                    <div class="default-cover d-flex justify-content-center align-items-center">
                                        <i class="fas fa-book fa-3x opacity-50"></i>
                                    </div>
                                <?php endif; ?>
                                
                                <!-- عنوان الرواية الظاهر دائماً -->
                                <div class="novel-title-overlay position-absolute bottom-0 start-0 w-100 p-3 text-white">
                                    <div class="title-gradient position-absolute top-0 start-0 w-100 h-100"></div>
                                    <h5 class="card-title mb-0 position-relative fw-bold">
                                        <?php the_title(); ?>
                                    </h5>
                                </div>
                                
                                <!-- المسطول العائم الشفاف الذي يظهر عند التمرير -->
                                <div class="novel-hover-info position-absolute top-0 start-0 w-100 h-100 d-flex flex-column justify-content-center p-3 opacity-0">
                                    <div class="hover-info-bg position-absolute top-0 start-0 w-100 h-100"></div>
                                    <div class="position-relative text-white">
                                        <h5 class="card-title mb-2 text-truncate" title="<?php the_title(); ?>"><?php the_title(); ?></h5>
                                        
                                        <?php if ($genres && !is_wp_error($genres)) : ?>
                                            <div class="genres mb-2 d-flex flex-wrap gap-1">
                                                <?php foreach (array_slice($genres, 0, 3) as $genre) : ?>
                                                    <span class="badge rounded-pill genre-badge">
                                                        <?php echo $genre->name; ?>
                                                    </span>
                                                <?php endforeach; ?>
                                            </div>
                                        <?php endif; ?>
                                        
                                        <div class="novel-excerpt mb-2 small">
                                            <?php echo $excerpt; ?>
                                        </div>
                                        
                                        <div class="d-flex justify-content-between align-items-center small mb-2">
                                            <div>
                                                <i class="fas fa-book-open me-1"></i>
                                                <span class="chapter-count"><?php echo sekaiplus_get_unique_chapter_count(get_the_ID()) . ' فصل'; ?></span>
                                            </div>
                                            <?php 
                                            // حساب متوسط التقييمات مباشرة
                                            $ratings = get_post_meta(get_the_ID(), '_novel_ratings', true);
                                            $actual_rating = 0;
                                            if (is_array($ratings) && !empty($ratings)) {
                                                $total = 0;
                                                $count = 0;
                                                foreach ($ratings as $user_rating) {
                                                    $total += $user_rating;
                                                    $count++;
                                                }
                                                if ($count > 0) {
                                                    $actual_rating = round($total / $count, 1);
                                                }
                                            } else {
                                                $actual_rating = $rating;
                                            }
                                            
                                            if ($actual_rating > 0) : 
                                            ?>
                                            <div class="rating-container">
                                                <i class="fas fa-star me-1"></i>
                                                <span class="rating-value"><?php echo number_format($actual_rating, 1); ?></span>
                                            </div>
                                            <?php else: ?>
                                            <div class="rating-container" style="display: none;">
                                                <i class="fas fa-star me-1"></i>
                                                <span class="rating-value">0.0</span>
                                            </div>
                                            <?php endif; ?>
                                        </div>
                                        
                                        <a href="<?php the_permalink(); ?>" class="btn btn-sm btn-light rounded-pill px-3 shadow-sm">
                                            <i class="fas fa-eye me-1"></i>
                                            عرض التفاصيل
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </article>
                </div>
            <?php
            endwhile;
            wp_reset_postdata();
            ?>
        </div>
    </section>

    <?php if (is_user_logged_in()) : ?>
    <!-- آخر الفصول المضافة - للمستخدمين المسجلين فقط -->
    <section class="latest-chapters-section mb-5">
        <div class="section-header d-flex justify-content-between align-items-center mb-4">
            <div class="d-flex align-items-center">
                <div class="section-icon rounded-circle p-3 me-3">
                    <i class="fas fa-clock fs-4"></i>
                </div>
                <h2 class="section-title h3 mb-0">آخر الفصول المضافة</h2>
            </div>
            <a href="<?php echo home_url('/latest/'); ?>" class="btn btn-sm btn-outline-primary rounded-pill px-3">
                عرض الكل
                <i class="fas fa-arrow-left ms-1"></i>
            </a>
        </div>

        <div class="card shadow-sm mb-4 border-0 rounded-4 overflow-hidden">
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover align-middle mb-0 chapters-table">
                        <thead class="table-light">
                            <tr>
                                <th scope="col" style="width: 60%">الفصل</th>
                                <th scope="col" style="width: 20%">المترجم</th>
                                <th scope="col" style="width: 20%">صدرت منذ</th>
                            </tr>
                        </thead>
                        <tbody id="latest-chapters">
                            <!-- سيتم ملء هذا الجزء من خلال نظام AJAX -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </section>
    <?php else : ?>
    <!-- محتوى للمستخدمين غير المسجلين -->
    <section class="guest-content-section mb-5">
        <!-- تنبيه التسجيل -->
        <div class="auth-prompt-container mb-4">
            <div class="auth-prompt-card card border-0 shadow-sm rounded-4 overflow-hidden">
                <div class="card-body p-0">
                    <div class="auth-prompt-content d-flex align-items-center justify-content-between p-4">
                        <div class="auth-prompt-info d-flex align-items-center">
                            <div class="auth-prompt-icon me-3">
                                <i class="fas fa-user-plus"></i>
                            </div>
                            <div class="auth-prompt-text">
                                <h5 class="mb-1 fw-semibold">حتى تحصل على تجربتك الكاملة</h5>
                                <p class="mb-0 text-muted small">سجل حساب جديد أو سجل دخولك لمتابعة آخر الفصول المضافة</p>
                            </div>
                        </div>
                        <div class="auth-prompt-actions d-flex gap-2">
                            <a href="<?php echo wp_login_url(home_url()); ?>" class="btn btn-outline-primary btn-sm rounded-pill px-3">
                                <i class="fas fa-sign-in-alt me-1"></i>
                                تسجيل الدخول
                            </a>
                            <a href="<?php echo wp_registration_url(); ?>" class="btn btn-primary btn-sm rounded-pill px-3">
                                <i class="fas fa-user-plus me-1"></i>
                                حساب جديد
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- أفضل 10 روايات -->
        <div class="top-novels-section">
            <div class="section-header d-flex justify-content-between align-items-center mb-4">
                <div class="d-flex align-items-center">
                    <div class="section-icon rounded-circle p-3 me-3">
                        <i class="fas fa-trophy fs-4"></i>
                    </div>
                    <h2 class="section-title h3 mb-0">أفضل 10 روايات</h2>
                </div>
                <a href="<?php echo home_url('/library/'); ?>" class="btn btn-sm btn-outline-primary rounded-pill px-3">
                    عرض الكل
                    <i class="fas fa-arrow-left ms-1"></i>
                </a>
            </div>

            <div class="card shadow-sm border-0 rounded-4 overflow-hidden">
                <div class="card-body p-0">
                    <?php
                    // الحصول على أفضل 10 روايات بناءً على التقييمات
                    $top_novels = new WP_Query(array(
                        'post_type' => 'novel',
                        'posts_per_page' => 10,
                        'meta_key' => '_average_rating',
                        'orderby' => 'meta_value_num',
                        'order' => 'DESC',
                        'meta_query' => array(
                            array(
                                'key' => '_average_rating',
                                'value' => 0,
                                'compare' => '>'
                            )
                        )
                    ));

                    // إذا لم توجد روايات مقيمة، اعرض الأحدث
                    if ($top_novels->post_count == 0) {
                        $top_novels = new WP_Query(array(
                            'post_type' => 'novel',
                            'posts_per_page' => 10,
                            'orderby' => 'date',
                            'order' => 'DESC'
                        ));
                    }

                    if ($top_novels->have_posts()) :
                        $rank = 1;
                        while ($top_novels->have_posts()) : $top_novels->the_post();
                            // الحصول على التقييمات
                            $user_ratings = get_post_meta(get_the_ID(), '_user_ratings', true);
                            $rating_count = is_array($user_ratings) ? count($user_ratings) : 0;
                            $average_rating = 0;

                            if ($rating_count > 0) {
                                $total_rating = 0;
                                foreach ($user_ratings as $rating) {
                                    $total_rating += $rating['rating'];
                                }
                                $average_rating = round($total_rating / $rating_count, 1);
                            }

                            // الحصول على التصنيفات
                            $genres = get_the_terms(get_the_ID(), 'novel_genre');

                            // الحصول على عدد الفصول
                            $chapter_count = sekaiplus_get_chapter_count(get_the_ID());

                            // الحصول على سنة النشر
                            $year = get_post_meta(get_the_ID(), '_novel_year', true);

                            // الحصول على الحالة
                            $status = get_post_meta(get_the_ID(), '_novel_status', true);
                    ?>
                        <div class="top-novel-item d-flex align-items-center p-3 border-bottom position-relative">
                            <div class="novel-rank me-3">
                                <span class="rank-number fw-bold fs-5 text-muted">#<?php echo $rank; ?></span>
                            </div>

                            <div class="novel-thumbnail rounded-3 overflow-hidden me-3">
                                <?php if (has_post_thumbnail()) : ?>
                                    <img src="<?php echo get_the_post_thumbnail_url(get_the_ID(), 'thumbnail'); ?>"
                                         alt="<?php echo esc_attr(get_the_title()); ?>"
                                         class="novel-cover-img">
                                <?php else : ?>
                                    <div class="default-thumbnail rounded-3">
                                        <i class="fas fa-book"></i>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <div class="novel-info flex-grow-1">
                                <h6 class="novel-title mb-1 fw-semibold">
                                    <a href="<?php the_permalink(); ?>" class="text-decoration-none"><?php the_title(); ?></a>
                                </h6>

                                <?php if ($genres && !is_wp_error($genres)) : ?>
                                <div class="novel-genres mb-2">
                                    <?php foreach (array_slice($genres, 0, 3) as $genre) : ?>
                                        <span class="badge rounded-pill me-1 genre-tag"><?php echo esc_html($genre->name); ?></span>
                                    <?php endforeach; ?>
                                </div>
                                <?php endif; ?>
                            </div>

                            <div class="novel-stats text-end">
                                <div class="rating-info mb-1">
                                    <span class="rating-circle d-inline-flex align-items-center justify-content-center rounded-circle me-2">
                                        <?php echo $average_rating > 0 ? number_format($average_rating, 1) : '0.0'; ?>
                                    </span>
                                    <span class="rating-users small text-muted"><?php echo number_format($rating_count); ?> مستخدم</span>
                                </div>

                                <div class="novel-meta small text-muted">
                                    <div class="novel-type mb-1">رواية</div>
                                    <div class="novel-chapters"><?php echo $chapter_count; ?> فصل</div>
                                    <?php if (!empty($year)) : ?>
                                    <div class="novel-year"><?php echo esc_html($year); ?></div>
                                    <?php endif; ?>
                                    <?php if (!empty($status)) : ?>
                                    <div class="novel-status">
                                        <span class="status-badge"><?php echo esc_html($status); ?></span>
                                    </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php
                        $rank++;
                        endwhile;
                        wp_reset_postdata();
                    else:
                    ?>
                        <div class="text-center p-4 text-muted">
                            <i class="fas fa-book fa-3x mb-3 opacity-50"></i>
                            <p>لا توجد روايات متاحة حالياً</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </section>
    <?php endif; ?>

    <!-- قالب الفصل -->
    <template id="chapter-template">
        <tr class="chapter-row">
            <td>
                <div class="d-flex align-items-center">
                    <div class="novel-thumbnail rounded-3 overflow-hidden me-2">
                        <img src="" alt="" class="novel-cover img-fluid" width="40" height="55">
                    </div>
                    <div>
                        <a href="#" class="novel-link d-block mb-1 small text-secondary"></a>
                        <a href="#" class="chapter-link fw-semibold"></a>
                    </div>
                </div>
            </td>
            <td>
                <div class="d-flex align-items-center translator-info">
                    <span class="text-truncate"><a href="#" class="translator-link small"></a></span>
                </div>
            </td>
            <td>
                <div class="chapter-date small text-secondary">
                    <i class="far fa-clock me-1"></i>
                    <span class="time-ago"></span>
                </div>
            </td>
        </tr>
    </template>

    <style>
        /* تنسيق قسم آخر الفصول */

.d-flex.align-items-center.translator-info {
    padding-left: 72%;
}

        /* تنسيق تنبيه التسجيل */
        .auth-prompt-container {
            margin-bottom: 2rem;
        }

        .auth-prompt-card {
            background: linear-gradient(135deg,
                <?php echo $dark_mode ? 'rgba(74, 144, 226, 0.1)' : 'rgba(74, 144, 226, 0.05)'; ?> 0%,
                <?php echo $dark_mode ? 'rgba(80, 200, 120, 0.1)' : 'rgba(80, 200, 120, 0.05)'; ?> 100%);
            border: 1px solid <?php echo $dark_mode ? 'rgba(74, 144, 226, 0.2)' : 'rgba(74, 144, 226, 0.15)'; ?>;
            transition: all 0.3s ease;
        }

        .auth-prompt-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(74, 144, 226, 0.15) !important;
        }

        .auth-prompt-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #4a90e2, #50c878);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
            box-shadow: 0 4px 15px rgba(74, 144, 226, 0.3);
        }

        .auth-prompt-text h5 {
            color: <?php echo $dark_mode ? '#e0e0e0' : '#2c3e50'; ?>;
            font-size: 1.1rem;
        }

        .auth-prompt-text p {
            color: <?php echo $dark_mode ? '#b0b0b0' : '#6c757d'; ?>;
            font-size: 0.9rem;
        }

        .auth-prompt-actions .btn {
            font-size: 0.85rem;
            padding: 0.5rem 1rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .auth-prompt-actions .btn-outline-primary {
            border-color: #4a90e2;
            color: #4a90e2;
        }

        .auth-prompt-actions .btn-outline-primary:hover {
            background-color: #4a90e2;
            border-color: #4a90e2;
            color: white;
            transform: translateY(-1px);
        }

        .auth-prompt-actions .btn-primary {
            background: linear-gradient(135deg, #4a90e2, #50c878);
            border: none;
            color: white;
        }

        .auth-prompt-actions .btn-primary:hover {
            background: linear-gradient(135deg, #357abd, #45b86b);
            transform: translateY(-1px);
            box-shadow: 0 4px 15px rgba(74, 144, 226, 0.4);
        }

        /* تنسيق موبايل للتنبيه */
        @media (max-width: 767.98px) {
            .auth-prompt-content {
                flex-direction: column;
                text-align: center;
                gap: 1rem;
            }

            .auth-prompt-info {
                flex-direction: column;
                text-align: center;
                gap: 1rem;
            }

            .auth-prompt-actions {
                justify-content: center;
                flex-wrap: wrap;
            }

            .auth-prompt-actions .btn {
                min-width: 120px;
            }
        }

        @media (max-width: 575.98px) {
            .auth-prompt-actions {
                flex-direction: column;
                width: 100%;
            }

            .auth-prompt-actions .btn {
                width: 100%;
                min-width: auto;
            }
        }

        /* تنسيق قائمة أفضل الروايات */
        .top-novels-section .section-icon {
            background: linear-gradient(135deg, #ffd700, #ffb347);
            color: white;
            box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
        }

        .top-novel-item {
            transition: all 0.3s ease;
            border-bottom: 1px solid <?php echo $dark_mode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)'; ?> !important;
            padding: 1rem !important;
            background-color: <?php echo $dark_mode ? 'rgba(30, 30, 30, 0.5)' : 'rgba(255, 255, 255, 0.8)'; ?>;
        }

        .top-novel-item:hover {
            background-color: <?php echo $dark_mode ? 'rgba(40, 40, 40, 0.7)' : 'rgba(245, 245, 245, 0.9)'; ?>;
            transform: translateX(-5px);
            box-shadow: 0 3px 10px <?php echo $dark_mode ? 'rgba(0, 0, 0, 0.3)' : 'rgba(0, 0, 0, 0.1)'; ?>;
            z-index: 1;
            position: relative;
        }

        .top-novel-item:last-child {
            border-bottom: none !important;
        }

        .top-novels-section .card {
            background: <?php echo $dark_mode ? 'rgba(25, 25, 25, 0.7)' : 'rgba(255, 255, 255, 0.9)'; ?>;
            border: 1px solid <?php echo $dark_mode ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.05)'; ?> !important;
        }

        .rank-number {
            color: <?php echo $dark_mode ? '#ffd700' : '#ff6b35'; ?> !important;
            font-size: 1.2rem !important;
            min-width: 35px;
            text-align: center;
        }

        .novel-title a {
            color: <?php echo $dark_mode ? '#e0e0e0' : '#2c3e50'; ?>;
            font-size: 1rem;
            line-height: 1.3;
        }

        .novel-title a:hover {
            color: #4a90e2;
        }

        .genre-tag {
            background: <?php echo $dark_mode ? 'rgba(74, 144, 226, 0.2)' : 'rgba(74, 144, 226, 0.1)'; ?>;
            color: <?php echo $dark_mode ? '#87ceeb' : '#4a90e2'; ?>;
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
            border: 1px solid <?php echo $dark_mode ? 'rgba(74, 144, 226, 0.3)' : 'rgba(74, 144, 226, 0.2)'; ?>;
        }

        .rating-circle {
            width: 35px;
            height: 35px;
            background: linear-gradient(135deg, #50c878, #32cd32);
            color: white;
            font-size: 0.8rem;
            font-weight: bold;
            box-shadow: 0 2px 8px rgba(80, 200, 120, 0.3);
        }

        .rating-users {
            color: <?php echo $dark_mode ? '#b0b0b0' : '#6c757d'; ?>;
            font-size: 0.8rem;
        }

        .novel-meta {
            color: <?php echo $dark_mode ? '#a0a0a0' : '#6c757d'; ?>;
            font-size: 0.75rem;
            line-height: 1.4;
        }

        .status-badge {
            background: <?php echo $dark_mode ? 'rgba(80, 200, 120, 0.2)' : 'rgba(80, 200, 120, 0.1)'; ?>;
            color: <?php echo $dark_mode ? '#90ee90' : '#50c878'; ?>;
            padding: 0.15rem 0.4rem;
            border-radius: 0.25rem;
            font-size: 0.7rem;
            border: 1px solid <?php echo $dark_mode ? 'rgba(80, 200, 120, 0.3)' : 'rgba(80, 200, 120, 0.2)'; ?>;
        }

        /* تحسين عرض صور أغلفة الروايات */
        .novel-thumbnail {
            width: 60px !important;
            height: 80px !important;
            background: <?php echo $dark_mode ? 'linear-gradient(135deg, #2c3e50, #34495e)' : 'linear-gradient(135deg, #f8f9fa, #e9ecef)'; ?>;
            border: 1px solid <?php echo $dark_mode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)'; ?>;
            position: relative;
            overflow: hidden !important;
            flex-shrink: 0;
            display: block;
        }

        .novel-thumbnail img,
        .novel-cover-img {
            width: 100% !important;
            height: 100% !important;
            object-fit: cover !important;
            object-position: center !important;
            border-radius: inherit;
            transition: transform 0.3s ease;
            display: block !important;
            background: <?php echo $dark_mode ? '#2c3e50' : '#f8f9fa'; ?>;
        }

        .novel-thumbnail:hover img,
        .novel-thumbnail:hover .novel-cover-img {
            transform: scale(1.05);
        }

        .default-thumbnail {
            background: <?php echo $dark_mode ? 'linear-gradient(135deg, #2c3e50, #34495e)' : 'linear-gradient(135deg, #f8f9fa, #e9ecef)'; ?> !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            width: 60px !important;
            height: 80px !important;
            border: 1px solid <?php echo $dark_mode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)'; ?>;
            flex-shrink: 0;
        }

        .default-thumbnail i {
            color: <?php echo $dark_mode ? '#7f8c8d' : '#6c757d'; ?> !important;
            font-size: 1.5rem !important;
        }

        /* إزالة أي خلفيات بيضاء غير مرغوب فيها */
        .top-novel-item * {
            background-color: transparent !important;
        }

        .top-novel-item .novel-thumbnail,
        .top-novel-item .default-thumbnail {
            background: <?php echo $dark_mode ? 'linear-gradient(135deg, #2c3e50, #34495e)' : 'linear-gradient(135deg, #f8f9fa, #e9ecef)'; ?> !important;
        }

        .top-novel-item img {
            background: <?php echo $dark_mode ? '#2c3e50' : '#f8f9fa'; ?> !important;
        }

        /* تنسيق موبايل لقائمة الروايات */
        @media (max-width: 767.98px) {
            .top-novel-item {
                flex-direction: column;
                align-items: flex-start !important;
                padding: 1rem !important;
            }

            .novel-rank {
                position: absolute;
                top: 1rem;
                right: 1rem;
                margin: 0 !important;
            }

            .novel-thumbnail {
                margin-bottom: 0.75rem !important;
                margin-right: 0 !important;
            }

            .novel-info {
                width: 100%;
                margin-bottom: 0.75rem;
            }

            .novel-stats {
                width: 100%;
                text-align: right !important;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }

            .rating-info {
                margin-bottom: 0 !important;
            }
        }

        @media (max-width: 575.98px) {
            .novel-stats {
                flex-direction: column;
                align-items: flex-end !important;
                gap: 0.5rem;
            }
        }
        .chapters-table {
            --bs-table-hover-bg: rgba(var(--bs-primary-rgb), 0.03);
        }
        
        .chapters-table thead th {
            font-weight: 600;
            border-top: none;
            border-bottom-width: 1px;
            padding: 1rem;
        }
        
        .chapters-table tbody tr {
            transition: all 0.2s ease;
            border-bottom: 1px solid var(--bs-border-color);
        }
        
        .chapters-table tbody tr:hover {
            box-shadow: 0 3px 10px rgba(0,0,0,0.05);
        }
        
        .chapters-table td {
            padding: 0.75rem 1rem;
            vertical-align: middle;
        }
        
        .novel-thumbnail {
            width: 40px;
            height: 55px;
            background-color: var(--bs-light);
            overflow: hidden;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .novel-cover {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .novel-link {
            color: var(--bs-secondary);
            font-size: 0.75rem;
            text-decoration: none;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 250px;
            display: inline-block;
        }
        
        .novel-link:hover {
            color: var(--bs-primary);
        }
        
        .chapter-link {
            color: var(--bs-body-color);
            text-decoration: none;
            font-size: 0.95rem;
            line-height: 1.4;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            max-width: 500px;
        }
        
        .chapter-link:hover {
            color: var(--bs-primary);
        }
        
        .translator-info {
            font-size: 0.85rem;
        }
        
        .translator-info img {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-left: 0.5rem;
            border: 1px solid var(--bs-border-color);
        }
        
        .translator-link {
            color: var(--bs-body-color);
            text-decoration: none;
        }
        
        .translator-link:hover {
            color: var(--bs-primary);
        }
        
        .chapter-date {
            color: var(--bs-secondary);
            font-size: 0.8rem;
        }
        
        /* تنسيق موبايل */
        @media (max-width: 767.98px) {
            .novel-thumbnail {
                width: 35px;
                height: 48px;
            }
            
            .chapter-link {
                font-size: 0.9rem;
                max-width: 250px;
            }
        }
        
        @media (max-width: 575.98px) {
            .novel-thumbnail {
                width: 30px;
                height: 42px;
            }
            
            .chapter-link {
                max-width: 200px;
            }
        }
        
        /* تنسيق وضع داكن */
        [data-bs-theme="dark"] .chapters-table {
            --bs-table-hover-bg: rgba(var(--bs-primary-rgb), 0.1);
        }
        
        [data-bs-theme="dark"] .novel-thumbnail {
            background-color: var(--bs-dark-bg-subtle);
        }
        
        /* تنسيق نتائج البحث السريع */
        .quick-search-results {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: #fff;
            z-index: 1000;
            max-height: 400px;
            overflow-y: auto;
            display: none;
            margin-top: 5px;
            border: 1px solid rgba(0,0,0,0.1);
        }

        .search-result {
            transition: background-color 0.2s ease;
        }

        .search-result:hover {
            background-color: rgba(var(--bs-primary-rgb), 0.05);
        }

        [data-bs-theme="dark"] .quick-search-results {
            background: var(--bs-dark-bg-subtle);
            border-color: rgba(255,255,255,0.1);
        }

        [data-bs-theme="dark"] .search-result h6 {
            color: #fff !important;
        }

        [data-bs-theme="dark"] .search-result:hover {
            background-color: rgba(255,255,255,0.05);
        }
        
        /* أنماط الروايات - مسطول ظاهري شفاف */
        .novel-card {
            transition: all 0.3s ease;
        }

        .novel-card-inner {
            overflow: hidden;
        }

        .novel-cover-link {
            aspect-ratio: 2/3;
            position: relative;
        }

        .novel-cover {
            transition: transform 0.5s ease;
        }

        .novel-card:hover .novel-cover {
            transform: scale(1.05);
        }

        /* عنوان الرواية الظاهر دائماً */
        .novel-title-overlay {
            z-index: 2;
        }

        .title-gradient {
            background: linear-gradient(to top, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0) 100%);
            z-index: -1;
        }

        /* المسطول الظاهري الشفاف */
        .novel-hover-info {
            z-index: 3;
            transition: opacity 0.3s ease;
        }

        .novel-card:hover .novel-hover-info {
            opacity: 1 !important;
        }

        .hover-info-bg {
            background-color: rgba(0, 0, 0, 0.75);
            backdrop-filter: blur(4px);
            z-index: -1;
        }

        .genre-badge {
            background-color: rgba(255, 255, 255, 0.2);
            color: white;
            font-size: 0.7rem;
        }

        .novel-excerpt {
            display: -webkit-box;
            -webkit-line-clamp: 3;
            line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
            line-height: 1.4;
        }

        /* تعديلات الوضع الداكن */
        .dark-theme .hover-info-bg {
            background-color: rgba(0, 0, 0, 0.85);
        }

        .dark-theme .genre-badge {
            background-color: rgba(255, 255, 255, 0.15);
        }
    </style>

    <script>
    jQuery(document).ready(function($) {
        // تحديث عدد الفصول والتقييمات
        $('.novel-card').each(function() {
            const novelId = $(this).data('novel-id');
            if (!novelId) return;
            
            // تحديث عدد الفصول
            $.ajax({
                url: sekaiplus_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'get_accurate_chapter_count',
                    novel_id: novelId,
                    nonce: sekaiplus_ajax.nonce
                },
                success: function(response) {
                    if (response.success && response.data) {
                        $(`.novel-card[data-novel-id="${novelId}"] .chapter-count`).text(response.data + ' فصل');
                    }
                }
            });
            
            // تحديث التقييم
            $.ajax({
                url: sekaiplus_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'get_accurate_rating',
                    novel_id: novelId,
                    nonce: sekaiplus_ajax.nonce
                },
                success: function(response) {
                    if (response.success && response.data > 0) {
                        $(`.novel-card[data-novel-id="${novelId}"] .rating-value`).text(response.data.toFixed(1));
                        $(`.novel-card[data-novel-id="${novelId}"] .rating-container`).show();
                    } else {
                        $(`.novel-card[data-novel-id="${novelId}"] .rating-container`).hide();
                    }
                }
            });
        });
    });
    </script>

    <?php
    // تحميل ملف JavaScript الخاص بآخر الفصول فقط للمستخدمين المسجلين
    if (is_user_logged_in()) {
        wp_enqueue_script(
            'sekaiplus-home-latest-chapters',
            get_template_directory_uri() . '/js/home-latest-chapters.js',
            array('jquery'),
            SEKAIPLUS_VERSION,
            true
        );

        // تمرير البيانات اللازمة للملف
        wp_localize_script('sekaiplus-home-latest-chapters', 'sekaiplus_home', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('sekaiplus_latest_chapters')
        ));
    }

    // تحميل ملف JavaScript الخاص بالبحث السريع
    wp_enqueue_script(
        'sekaiplus-quick-search', 
        get_template_directory_uri() . '/js/quick-search.js', 
        array('jquery'), 
        SEKAIPLUS_VERSION, 
        true
    );

    // تمرير البيانات اللازمة لملف البحث السريع
    wp_localize_script('sekaiplus-quick-search', 'sekaiplus_search', array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('sekaiplus_ajax_nonce')
    ));

    // تحميل كود Swiper
    wp_add_inline_script('swiper', "document.addEventListener('DOMContentLoaded', function() {\n"
        . "    // تهيئة سلايدر الروايات المميزة\n"
        . "    const featuredSwiper = new Swiper('.featured-novels-slider', {\n"
        . "        autoplay: {\n"
        . "            delay: 5000,\n"
        . "            disableOnInteraction: false,\n"
        . "        },\n"
        . "        loop: true,\n"
        . "        effect: 'fade',\n"
        . "        fadeEffect: {\n"
        . "            crossFade: true\n"
        . "        },\n"
        . "        speed: 800,\n"
        . "        pagination: {\n"
        . "            el: '.swiper-pagination',\n"
        . "            clickable: true,\n"
        . "        }\n"
        . "    });\n"
        . "\n"
        . "    // تطبيق تأثيرات التحويم على بطاقات الروايات\n"
        . "    const novelCards = document.querySelectorAll('.novel-card');\n"
        . "    novelCards.forEach(card => {\n"
        . "        const overlay = card.querySelector('.novel-hover-overlay');\n"
        . "        if (overlay) {\n"
        . "            card.addEventListener('mouseenter', function() {\n"
        . "                overlay.style.opacity = '1';\n"
        . "                overlay.style.visibility = 'visible'; // إظهار الظل بالكامل عند التحويم\n"
        . "            });\n"
        . "            card.addEventListener('mouseleave', function() {\n"
        . "                overlay.style.opacity = '0';\n"
        . "                // منع إخفاء الظل مباشرة عند إزالة التحويم\n"
        . "                setTimeout(() => { overlay.style.visibility = 'hidden'; }, 300);\n"
        . "            });\n"
        . "        }\n"
        . "    });\n"
        . "});");

    get_footer();
    ?>
